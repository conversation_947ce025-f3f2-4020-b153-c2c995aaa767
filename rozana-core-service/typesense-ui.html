<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Typesense UI</title>
    <script src="https://cdn.jsdelivr.net/npm/typesense@1.7.1/dist/typesense.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { padding: 20px; }
        pre { background-color: #f5f5f5; padding: 15px; border-radius: 5px; }
        .collection-card { margin-bottom: 20px; cursor: pointer; }
        .collection-card:hover { box-shadow: 0 0 10px rgba(0,0,0,0.1); }
        #searchResults { margin-top: 20px; }
        .search-result { margin-bottom: 10px; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">Typesense UI</h1>
        
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Connection Settings</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="host" class="form-label">Host</label>
                                    <input type="text" class="form-control" id="host" value="localhost">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="port" class="form-label">Port</label>
                                    <input type="text" class="form-control" id="port" value="8108">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="protocol" class="form-label">Protocol</label>
                                    <select class="form-select" id="protocol">
                                        <option value="http">HTTP</option>
                                        <option value="https">HTTPS</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="apiKey" class="form-label">API Key</label>
                                    <input type="text" class="form-control" id="apiKey" value="xyz">
                                </div>
                            </div>
                        </div>
                        <button id="connectBtn" class="btn btn-primary">Connect</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mb-4" id="collectionsSection" style="display: none;">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5>Collections</h5>
                        <button id="refreshCollectionsBtn" class="btn btn-sm btn-outline-secondary">Refresh</button>
                    </div>
                    <div class="card-body">
                        <div id="collections" class="row"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mb-4" id="collectionDetailSection" style="display: none;">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Collection: <span id="collectionName"></span></h5>
                    </div>
                    <div class="card-body">
                        <ul class="nav nav-tabs" id="collectionTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="schema-tab" data-bs-toggle="tab" data-bs-target="#schema" type="button" role="tab">Schema</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="search-tab" data-bs-toggle="tab" data-bs-target="#search" type="button" role="tab">Search</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="documents-tab" data-bs-toggle="tab" data-bs-target="#documents" type="button" role="tab">Documents</button>
                            </li>
                        </ul>
                        <div class="tab-content p-3" id="collectionTabsContent">
                            <div class="tab-pane fade show active" id="schema" role="tabpanel">
                                <pre id="schemaContent"></pre>
                            </div>
                            <div class="tab-pane fade" id="search" role="tabpanel">
                                <div class="mb-3">
                                    <label for="searchQuery" class="form-label">Search Query (type to search)</label>
                                    <input type="text" class="form-control" id="searchQuery" placeholder="Start typing to search...">
                                    <small class="form-text text-muted">Search will start automatically after you stop typing</small>
                                </div>
                                <div class="mb-3">
                                    <label for="debounceTime" class="form-label">Debounce Time: <span id="debounceValue">300</span> ms</label>
                                    <input type="range" class="form-range" id="debounceTime" value="300" min="100" max="2000" step="50">
                                    <div class="d-flex justify-content-between">
                                        <small class="text-muted">Fast (100ms)</small>
                                        <small class="text-muted">Slow (2000ms)</small>
                                    </div>
                                    <small class="form-text text-muted">Time to wait after typing before searching</small>
                                </div>
                                <div id="searchStatus" class="mb-3"></div>
                                <div id="searchResults"></div>
                            </div>
                            <div class="tab-pane fade" id="documents" role="tabpanel">
                                <button id="loadDocumentsBtn" class="btn btn-primary mb-3">Load Documents</button>
                                <div id="documentsList"></div>
                                <div id="documentsPagination" class="mt-3"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let client;
        let currentCollection;
        let documentsPage = 1;
        const documentsPerPage = 10;

        document.getElementById('connectBtn').addEventListener('click', connect);
        document.getElementById('refreshCollectionsBtn').addEventListener('click', fetchCollections);
        document.getElementById('loadDocumentsBtn').addEventListener('click', () => loadDocuments(1));
        
        // Setup debounced search on input
        const searchInput = document.getElementById('searchQuery');
        const debounceSlider = document.getElementById('debounceTime');
        const debounceValue = document.getElementById('debounceValue');
        const searchStatus = document.getElementById('searchStatus');
        let debounceTimeout;
        let isTyping = false;
        
        // Update debounce value display when slider changes
        debounceSlider.addEventListener('input', function() {
            debounceValue.textContent = this.value;
        });
        
        searchInput.addEventListener('input', function() {
            const debounceTime = parseInt(debounceSlider.value) || 300;
            clearTimeout(debounceTimeout);
            isTyping = true;
            
            // Update status indicator
            searchStatus.innerHTML = `
                <div class="alert alert-info">
                    <div class="d-flex align-items-center">
                        <div class="spinner-border spinner-border-sm me-2" role="status">
                            <span class="visually-hidden">Waiting...</span>
                        </div>
                        <span>Waiting for you to stop typing... (${debounceTime}ms)</span>
                    </div>
                </div>
            `;
            
            // Set debounce timeout
            debounceTimeout = setTimeout(() => {
                isTyping = false;
                searchStatus.innerHTML = '';
                searchCollection();
            }, debounceTime);
        });
        
        // Clear search when input is empty
        searchInput.addEventListener('change', function() {
            if (this.value.trim() === '') {
                clearTimeout(debounceTimeout);
                isTyping = false;
                searchStatus.innerHTML = '';
                document.getElementById('searchResults').innerHTML = '';
            }
        });

        function toggleDetails(button) {
            const detailsDiv = button.nextElementSibling;
            if (detailsDiv.style.display === 'none') {
                detailsDiv.style.display = 'block';
                button.textContent = 'Hide Details';
            } else {
                detailsDiv.style.display = 'none';
                button.textContent = 'Show Details';
            }
        }
        
        function connect() {
            const host = document.getElementById('host').value;
            const port = document.getElementById('port').value;
            const protocol = document.getElementById('protocol').value;
            const apiKey = document.getElementById('apiKey').value;

            client = new Typesense.Client({
                'nodes': [{
                    'host': host,
                    'port': port,
                    'protocol': protocol
                }],
                'apiKey': apiKey,
                'connectionTimeoutSeconds': 2
            });

            fetchCollections();
        }

        async function fetchCollections() {
            try {
                const collections = await client.collections().retrieve();
                displayCollections(collections);
                document.getElementById('collectionsSection').style.display = 'block';
            } catch (error) {
                alert('Error fetching collections: ' + error.message);
                console.error(error);
            }
        }

        function displayCollections(collections) {
            const collectionsContainer = document.getElementById('collections');
            collectionsContainer.innerHTML = '';

            if (collections.length === 0) {
                collectionsContainer.innerHTML = '<div class="col-12"><p>No collections found.</p></div>';
                return;
            }

            collections.forEach(collection => {
                const card = document.createElement('div');
                card.className = 'col-md-4';
                card.innerHTML = `
                    <div class="card collection-card">
                        <div class="card-body">
                            <h5 class="card-title">${collection.name}</h5>
                            <p class="card-text">Fields: ${collection.fields.length}</p>
                            <p class="card-text">Documents: ${collection.num_documents}</p>
                        </div>
                    </div>
                `;
                card.addEventListener('click', () => showCollectionDetails(collection));
                collectionsContainer.appendChild(card);
            });
        }

        function showCollectionDetails(collection) {
            currentCollection = collection;
            document.getElementById('collectionName').textContent = collection.name;
            document.getElementById('schemaContent').textContent = JSON.stringify(collection, null, 2);
            document.getElementById('collectionDetailSection').style.display = 'block';
            
            // Set default query_by fields
            const queryByFields = collection.fields
                .filter(field => field.type === 'string' || field.type === 'string[]')
                .map(field => field.name)
                .join(',');
            document.getElementById('queryBy').value = queryByFields;
            
            // Reset search and documents tabs
            document.getElementById('searchResults').innerHTML = '';
            document.getElementById('documentsList').innerHTML = '';
            document.getElementById('documentsPagination').innerHTML = '';
        }

        async function searchCollection() {
            const query = document.getElementById('searchQuery').value;
            const resultsContainer = document.getElementById('searchResults');
            
            // Handle empty query - show all documents with a wildcard search
            const searchQuery = query.trim() === '' ? '*' : query.trim();
            
            // Show loading indicator
            resultsContainer.innerHTML = `
                <div class="d-flex align-items-center">
                    <div class="spinner-border spinner-border-sm me-2" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <span>Searching...</span>
                </div>
            `;
            
            try {
                const results = await client.collections(currentCollection.name)
                    .documents()
                    .search({
                        'q': searchQuery,
                        'query_by': 'name,description,brand,sku',
                        'highlight_full_fields': 'name,description',
                        'per_page': 10,
                        'sort_by': '_text_match:desc'
                    });
                
                displaySearchResults(results);
            } catch (error) {
                console.error('Search error:', error);
                resultsContainer.innerHTML = `<div class="alert alert-danger">
                    <strong>Error searching:</strong> ${error.message}
                </div>`;
            }
        }

        function displaySearchResults(results) {
            const resultsContainer = document.getElementById('searchResults');
            resultsContainer.innerHTML = '';
            
            if (!results.hits || results.hits.length === 0) {
                resultsContainer.innerHTML = '<p>No results found.</p>';
                return;
            }
            
            resultsContainer.innerHTML = `<h5>Found ${results.found} results in ${results.search_time_ms}ms</h5>`;
            
            results.hits.forEach(hit => {
                const resultDiv = document.createElement('div');
                resultDiv.className = 'search-result';
                
                // Get highlighted content if available
                let highlightedName = hit.document.name;
                let highlightedDescription = hit.document.description || '';
                
                if (hit.highlights) {
                    hit.highlights.forEach(highlight => {
                        if (highlight.field === 'name' && highlight.matched_tokens.length > 0) {
                            highlightedName = highlight.snippet;
                        }
                        if (highlight.field === 'description' && highlight.matched_tokens.length > 0) {
                            highlightedDescription = highlight.snippet;
                        }
                    });
                }
                
                // Create result HTML with better formatting and highlights
                resultDiv.innerHTML = `
                    <h5>${highlightedName}</h5>
                    <p>${highlightedDescription}</p>
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>SKU:</strong> ${hit.document.sku || 'N/A'}</p>
                            <p><strong>Price:</strong> ${hit.document.price || 'N/A'}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Brand:</strong> ${hit.document.brand || 'N/A'}</p>
                            <p><strong>Categories:</strong> ${Array.isArray(hit.document.categories) ? hit.document.categories.join(', ') : 'N/A'}</p>
                        </div>
                    </div>
                    <button class="btn btn-sm btn-outline-primary mt-2" onclick="toggleDetails(this)">Show Details</button>
                    <div class="mt-2" style="display: none;">
                        <pre>${JSON.stringify(hit.document, null, 2)}</pre>
                    </div>
                `;
                resultsContainer.appendChild(resultDiv);
            });
        }

        async function loadDocuments(page) {
            documentsPage = page;
            try {
                // Use search with empty query to get all documents instead of export
                // This provides better formatted results
                const searchResults = await client.collections(currentCollection.name)
                    .documents()
                    .search({
                        'q': '*',
                        'per_page': 100,
                        'page': page
                    });
                
                if (searchResults && searchResults.hits) {
                    const documents = searchResults.hits.map(hit => hit.document);
                    displayDocuments(documents, page, searchResults.found);
                } else {
                    displayDocuments([], page, 0);
                }
            } catch (error) {
                console.error('Error loading documents:', error);
                document.getElementById('documentsList').innerHTML = 
                    `<div class="alert alert-danger">Error loading documents: ${error.message}</div>`;
            }
        }

        function displayDocuments(documents, page, totalFound) {
            const documentsContainer = document.getElementById('documentsList');
            documentsContainer.innerHTML = '';
            
            if (!documents || documents.length === 0) {
                documentsContainer.innerHTML = '<p>No documents found.</p>';
                return;
            }
            
            // Display each document
            documents.forEach(doc => {
                if (!doc) return; // Skip undefined documents
                
                const docDiv = document.createElement('div');
                docDiv.className = 'search-result';
                
                // Get a display name for the document
                const displayName = doc.name || doc.id || 'Document';
                
                // Create document HTML with better formatting
                docDiv.innerHTML = `
                    <h5>${displayName}</h5>
                    <pre>${JSON.stringify(doc, null, 2)}</pre>
                `;
                documentsContainer.appendChild(docDiv);
            });
            
            // Create pagination
            const totalItems = totalFound || documents.length;
            const totalPages = Math.ceil(totalItems / documentsPerPage);
            const paginationContainer = document.getElementById('documentsPagination');
            paginationContainer.innerHTML = '';
            
            if (totalPages > 1) {
                const pagination = document.createElement('nav');
                pagination.innerHTML = `
                    <ul class="pagination">
                        <li class="page-item ${page === 1 ? 'disabled' : ''}">
                            <a class="page-link" href="#" data-page="${page - 1}">Previous</a>
                        </li>
                        ${Array.from({length: totalPages}, (_, i) => i + 1).map(p => `
                            <li class="page-item ${p === page ? 'active' : ''}">
                                <a class="page-link" href="#" data-page="${p}">${p}</a>
                            </li>
                        `).join('')}
                        <li class="page-item ${page === totalPages ? 'disabled' : ''}">
                            <a class="page-link" href="#" data-page="${page + 1}">Next</a>
                        </li>
                    </ul>
                `;
                paginationContainer.appendChild(pagination);
                
                // Add event listeners to pagination links
                const pageLinks = paginationContainer.querySelectorAll('.page-link');
                pageLinks.forEach(link => {
                    link.addEventListener('click', (e) => {
                        e.preventDefault();
                        const newPage = parseInt(e.target.dataset.page);
                        if (newPage >= 1 && newPage <= totalPages) {
                            loadDocuments(newPage);
                        }
                    });
                });
            }
        }
    </script>
</body>
</html>

"""
URL configuration for coreservice project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings

from authz import urls as authz_urls
from products import urls as products_urls
from orders import urls as orders_urls

from .sentry_views import test_sentry_error, sentry_status, trigger_custom_error


urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/auth/', include(authz_urls), name='authz'),
    path('api/products/', include(products_urls), name='products'),
    path('', include(orders_urls), name='orders'),
]

if not settings.DEBUG:
    urlpatterns += [
        path('api/sentry/test-error/', test_sentry_error, name='test_sentry_error'),
        path('api/sentry/status/', sentry_status, name='sentry_status'),
        path('api/sentry/custom-error/', trigger_custom_error, name='trigger_custom_error'),
    ]
else:
    import debug_toolbar
    urlpatterns += [
        path('__debug__/', include(debug_toolbar.urls)),
    ]

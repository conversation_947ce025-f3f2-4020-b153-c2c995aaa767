import typesense
import time
import requests
from django.conf import settings
import os

from products.typesense.schemas.main import products_schema, categories_schema, subcategories_schema


def get_typesense_client():
    """
    Get a configured Typesense client instance
    """
    client = typesense.Client({
        'api_key': settings.TYPESENSE_API_KEY,
        'nodes': [{
            'host': settings.TYPESENSE_HOST,
            'port': settings.TYPESENSE_PORT,
            'protocol': settings.TYPESENSE_PROTOCOL
        }],
        'connection_timeout_seconds': 5,
        'retry_interval_seconds': 2,
        'num_retries': 3
    })
    return client

def check_typesense_health(self):
    """Check if Typesense is healthy"""
    self.stdout.write('Checking Typesense health...')
    max_attempts = 5
    attempt = 0
        
    while attempt < max_attempts:
        attempt += 1
        try:
            # Use direct HTTP request to check health
            typesense_host = os.environ.get('TYPESENSE_HOST', 'localhost')
            typesense_port = os.environ.get('TYPESENSE_PORT', '8108')
            typesense_protocol = os.environ.get('TYPESENSE_PROTOCOL', 'http')
            typesense_api_key = os.environ.get('TYPESENSE_API_KEY', 'xyz')
                
            health_url = f"{typesense_protocol}://{typesense_host}:{typesense_port}/health"
            headers = {"X-TYPESENSE-API-KEY": typesense_api_key}
            
            response = requests.get(health_url, headers=headers, timeout=5)
            if response.status_code == 200:
                self.stdout.write(self.style.SUCCESS('✅ Typesense is healthy'))
                return True
            else:
                self.stdout.write(self.style.WARNING(f'Typesense health check failed with status {response.status_code}'))
        except Exception as e:
            self.stdout.write(self.style.WARNING(f'Typesense health check attempt {attempt} failed: {e}'))
            
        if attempt < max_attempts:
            self.stdout.write(f'Retrying in 2 seconds... (attempt {attempt}/{max_attempts})')
            time.sleep(2)
        
    self.stdout.write(self.style.ERROR('❌ Typesense health check failed after multiple attempts'))
    return False

def initialize_typesense_schema():
    """
    Initialize the Typesense schema for products collection
    """
    client = get_typesense_client()
    
    
    # Check if Typesense is ready by making a direct HTTP request to the health endpoint
    try:
        # Make a direct HTTP request to the health endpoint
        health_url = f"{settings.TYPESENSE_PROTOCOL}://{settings.TYPESENSE_HOST}:{settings.TYPESENSE_PORT}/health"
        headers = {"X-TYPESENSE-API-KEY": settings.TYPESENSE_API_KEY}
        response = requests.get(health_url, headers=headers, timeout=5)
        response.raise_for_status()  # Raise an exception for 4XX/5XX responses
        
        if not response.json().get('ok', False):
            print("Typesense health check failed: Service reported not OK")
            raise Exception("Typesense health check failed")
            
        # Check if collection exists, if not create it
        try:
            collections = client.collections.retrieve()
            collection_names = [collection['name'] for collection in collections]

            for base_schema in [products_schema, categories_schema, subcategories_schema]:
                if base_schema['name'] not in collection_names:
                    client.collections.create(base_schema)
                    print(f"{base_schema['name']} collection created in Typesense")
                else:
                    # Update the schema if it already exists
                    try:
                        client.collections[base_schema['name']].delete()
                        client.collections.create(base_schema)
                        print(f"{base_schema['name']} collection updated in Typesense")
                    except Exception as e:
                        print(f"Error updating {base_schema['name']} collection: {e}")
                        # If we can't update, continue with the existing schema
                        pass
        except Exception as e:
            print(f"Error updating {base_schema['name']} collection: {e}")
            # If we can't update, continue with the existing schema
            pass

    except Exception as e:
        print(f"Error initializing Typesense schema: {e}")
        raise

from rest_framework import serializers
from products.models import Product, Category, Brand, ProductImage
from products.models.product import ProductVariant, Collection, ProductLinkVariant



class CategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Category
        fields = ['id', 'name', 'description', 'image', 'is_active']


class CategoryListSerializer(serializers.ModelSerializer):
    parent = CategorySerializer(many=True, read_only=True)
    class Meta:
        model = Category
        fields = ['id', 'name', 'description', 'image', 'is_active', 'parent']


class BrandSerializer(serializers.ModelSerializer):
    class Meta:
        model = Brand
        fields = ['id', 'name', 'description', 'image', 'is_active']


class ProductSerializer(serializers.ModelSerializer):
    facility_name = serializers.CharField(source='facility.name', read_only=True)
    
    class Meta:
        model = Product
        fields = '__all__'
        read_only_fields = ('id', 'creation_date', 'updation_date')


class ProductSearchSerializer(serializers.Serializer):
    query = serializers.CharField(required=False, allow_blank=True)
    category = serializers.CharField(required=False, allow_blank=True)
    facility_id = serializers.CharField(required=False, allow_blank=True)
    brand = serializers.CharField(required=False, allow_blank=True)
    min_price = serializers.DecimalField(required=False, max_digits=10, decimal_places=2)
    max_price = serializers.DecimalField(required=False, max_digits=10, decimal_places=2)
    sort_by = serializers.CharField(required=False, default='name')
    sort_order = serializers.ChoiceField(choices=['asc', 'desc'], default='asc', required=False)


class ProductImageViewSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductImage
        fields = ['id','image','priority','alt_text','is_primary']

class ProductVariantViewSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductVariant
        fields = [
            'id','name','slug','sku','price','mrp',
            'ean_number','hsn_code', 'size','color','weight','net_qty','packaging_type','is_active'
        ]

class ProductCollectionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Collection
        fields = ['id','name']

class ProductLinkVariantViewSerializer(serializers.ModelSerializer):
    linked_variant = ProductVariantViewSerializer(read_only=True)  # Serialize the related ProductVariant
    class Meta:
        model = ProductLinkVariant
        fields = ['id', 'product','linked_variant']  # Include the linked variant details, not just the ID

class ProductListSerializer(serializers.ModelSerializer):
    category     = CategorySerializer(read_only=True)
    brand        = BrandSerializer(read_only=True)

    product_images  = ProductImageViewSerializer(many=True, required=False)
    variants        = ProductVariantViewSerializer(many=True, required=False)
    collections     = ProductCollectionSerializer(many=True, required=False)
    linked_variants = ProductLinkVariantViewSerializer(many=True,read_only=True)  # Serialize linked variants
    class Meta:
        model = Product
        fields = [
            'id','name','slug','sku','tags','description','category',
            'brand','is_active','is_published','product_images','variants','collections','linked_variants'
        ]

class ProductImageSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductImage
        fields = ['id','image','priority','alt_text','is_primary']

class ProductVariantSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductVariant
        fields = [
            'id','name','price','mrp','ssp','ean_number','hsn_code','size',
            'color','weight','net_qty','packaging_type','is_active'
        ]

class ProductDetailSerializer(serializers.ModelSerializer):
    category       = serializers.PrimaryKeyRelatedField(queryset=Category.objects.all())
    brand          = serializers.PrimaryKeyRelatedField(queryset=Brand.objects.all())

    product_images  = ProductImageSerializer(many=True, required=False)
    variants        = ProductVariantSerializer(many=True, required=False)
    facility_ids    = serializers.ListField(child=serializers.IntegerField(), required=False, write_only=True)
    collections    = serializers.ListField(child=serializers.IntegerField(), required=False, write_only=True)
    linked_variants = serializers.ListField(child=serializers.IntegerField(), required=False, write_only=True)

    class Meta:
        model = Product
        fields = [
            'id','name','description','tags','category','brand','top_product',
            'additional_details','is_active','is_published','is_active','image_url','image',
            'product_images','variants','facility_ids', 'collections', 'linked_variants'
        ]


class CollectionProductSerializer(serializers.ModelSerializer):
    class Meta:
        model = Product
        fields = ['id', 'name']

class CollectionListSerializer(serializers.ModelSerializer):
    products = CollectionProductSerializer(many=True, required=False)
    class Meta:
        model = Collection
        fields = '__all__'
class CollectionSerializer(serializers.ModelSerializer):
    # products = CollectionProductSerializer(many=True, required=False)
    products = serializers.PrimaryKeyRelatedField(queryset=Product.objects.all(), many=True)
    class Meta:
        model = Collection
        fields = '__all__'


class ProductVariantListSerializer(serializers.ModelSerializer):
    product = CollectionProductSerializer(many=False, required=False)    
    
    class Meta:
        model = ProductVariant
        fields = '__all__'



class ProductStatusUpdateSerializer(serializers.Serializer):
    is_active = serializers.BooleanField()  # Only accepts True or False

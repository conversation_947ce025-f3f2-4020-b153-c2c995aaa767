from django.db import models
from django.core.files.base import ContentFile
from django.db.models.signals import post_save, post_delete, m2m_changed, pre_delete, pre_save
from django.dispatch import receiver
from authz.models.tenant import TenantModel
from products.models.category import Category, Brand
from products.models.models import BaseModel
from coreservice.storage_backends import CategoryImageStorage
from products.utils.image_processing import convert_to_webp, process_webp_images, cleanup_images, cleanup_old_images

def product_image_upload_path(instance, filename):
    """Generate upload path for product thumbnail images"""
    return f"Product/Thumbnail/{instance.sku}.webp"

class Language(models.Model):
    name = models.CharField(max_length=50)  # e.g. "English", "Hindi", "Kannada", etc.
    
    def __str__(self):
        return self.name

class Product(BaseModel):
    name = models.CharField(max_length=255)
    slug = models.CharField(max_length=255, blank=True, null=True)
    sku = models.CharField(max_length=255, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    tags = models.JSONField(default=list, blank=True, null=True)
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='products')
    brand = models.ForeignKey(Brand, on_delete=models.CASCADE, blank=True, null=True, related_name='products')
    top_product = models.BooleanField(default=False)
    additional_details = models.TextField(blank=True, null=True)
    is_published= models.BooleanField(default=True)
    is_active = models.BooleanField(default=True)
    image_url = models.CharField(max_length=512, blank=True, null=True)
    image = models.TextField(blank=True, null=True)

    class Meta:
        db_table = 'products'
        # Removed unique_together constraint referencing non-existent 'facility' field

    def __str__(self):
        return self.name

    # def save(self, *args, **kwargs):
    #     process_image_upload(self, self.thumbnail_image, 'product')
    #     super().save(*args, **kwargs)

    def to_typesense_dict(self):
        return {
            'id': str(self.id),
            'name': self.name,
            'description': self.description or '',
            'sku': self.sku or '',
            'slug': self.slug or '',
            'brand': self.brand.name if self.brand else '',
            'category': self.category.name if self.category else '',
            'is_active': self.is_active,
            'is_published': self.is_published,
            'top_product': self.top_product,
            'created_at': self.creation_date.isoformat(),
            'updated_at': self.updation_date.isoformat(),
            'image_url': self.image_url or '',
            'image': self.image or '',
            'tags': self.tags or [],
            'additional_details': self.additional_details or '',
        }


@receiver(post_save, sender=Product)
def process_product_images_async(sender, instance, created, **kwargs):
    if instance.image_url or instance.image:
        # Process images if needed
        pass


@receiver(post_save, sender=Product)
def update_typesense_on_save(sender, instance, created, **kwargs):
    from products.typesense.client import get_typesense_client
    if hasattr(instance, '_typesense_update_complete') and instance._typesense_update_complete:
        return
    try:
        client = get_typesense_client()
        document = instance.to_typesense_dict()
        client.collections['products'].documents.upsert(document)
        print(f"Product {instance.id} indexed in Typesense")
    except Exception as e:
        print(f"Error indexing product {instance.id} in Typesense: {e}")


@receiver(post_delete, sender=Product)
def delete_from_typesense_on_delete(sender, instance, **kwargs):
    from products.typesense.client import get_typesense_client
    try:
        client = get_typesense_client()
        client.collections['products'].documents[str(instance.id)].delete()
        print(f"Product {instance.id} deleted from Typesense")
    except Exception as e:
        print(f"Error deleting product {instance.id} from Typesense: {e}")


# @receiver(m2m_changed, sender=Product.categories.through)
# def update_typesense_on_m2m_change(sender, instance, action, **kwargs):
#     if action not in ('post_add', 'post_remove', 'post_clear'):
#         return
#     try:
#         instance._typesense_update_complete = True
#         update_typesense_on_save(sender=Product, instance=instance, created=False)
#     except Exception as e:
#         print(f"Typesense update error: {e}")



@receiver(pre_delete, sender=Product)
def delete_product_image_on_delete(sender, instance, **kwargs):
    if instance.thumbnail_image:
        try:
            instance.thumbnail_image.delete(save=False)
            print(f"Deleted image for product {instance.sku}: {instance.thumbnail_image.name}")
        except Exception as e:
            print(f"Warning: Could not delete image for product {instance.sku}: {e}")
    cleanup_images(instance, 'Product')


@receiver(pre_save, sender=Product)
def delete_old_product_image_on_change(sender, instance, **kwargs):
    if not instance.pk:
        return
    try:
        old_instance = Product.objects.get(pk=instance.pk)
    except Product.DoesNotExist:
        return
    cleanup_old_images(old_instance, instance, 'Product')


class Tax(BaseModel):
    name            = models.CharField(max_length=100)  # Name of the tax (e.g., "GST 18%")
    percentage      = models.DecimalField(max_digits=5, decimal_places=2)  # The total tax percentage (e.g., 18.00 for 18%)
    cgst_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)  # CGST percentage
    sgst_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)  # SGST percentage
    igst_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)  # IGST percentage
    cess_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)  # Cess percentage (e.g., for luxury goods or sin goods)
    description     = models.TextField(blank=True, null=True)  # Optional description of the tax
    is_active       = models.BooleanField(default=True)

    
    def __str__(self):
        return f"{self.name} - {self.percentage}%"

    class Meta:
        db_table = 'taxes'
        verbose_name = "Tax"
        verbose_name_plural = "Taxes"


class ProductDetail(models.Model):
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='product_details')
    language = models.ForeignKey(Language, on_delete=models.CASCADE)
    name = models.CharField(max_length=255, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    tags = models.JSONField(default=list, blank=True, null=True)

    class Meta:
        unique_together = ('product', 'language')  # Ensure each product has only one record per language

    def __str__(self):
        return f"{self.product.name} - {self.language.name}"


class ProductVariant(BaseModel):
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='variants')
    name = models.CharField(max_length=255)
    slug = models.CharField(max_length=255, blank=True, null=True)
    sku = models.CharField(max_length=255, blank=True, null=True)
    price = models.FloatField(blank=True, null=True)
    mrp = models.FloatField(blank=True, null=True)
    ssp = models.FloatField(blank=True, null=True)
    ean_number = models.BigIntegerField(blank=True, null=True)
    hsn_code = models.TextField(blank=True, null=True)
    size = models.CharField(max_length=255, blank=True, null=True)
    color = models.CharField(max_length=255, blank=True, null=True)
    weight = models.CharField(max_length=255, blank=True, null=True)
    net_qty = models.CharField(max_length=255, blank=True, null=True)
    packaging_type = models.CharField(max_length=255, blank=True, null=True)
    image = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = 'variants'
        unique_together = ('name', 'sku')

    def save(self, *args, **kwargs):
        # Automatically generate SKU if it's not provided
        if not self.sku:
            prefix = 'ROZ'
            count = ProductVariant.objects.count() + 1
            self.sku = f'{prefix}{count:02d}'

        # Generate slug
        if not self.slug:
            # Start with product name and variant name
            # Use getattr to safely access product name
            product_name = getattr(self.product, 'name', '') if self.product else ''
            slug_parts = [slugify(product_name)]

            # Append optional fields if they exist
            if self.size:
                slug_parts.append(slugify(self.size))
            if self.color:
                slug_parts.append(slugify(self.color))
            if self.weight:
                slug_parts.append(slugify(self.weight))

            # Combine all parts into one slug
            self.slug = '-'.join(slug_parts)

        super(ProductVariant, self).save(*args, **kwargs)
        
        self.create_product_details()

    def create_product_details(self):
        # Get all available languages
        languages = Language.objects.all()

        # Create or update product details for each language
        for language in languages:
            # Check if ProductDetail exists for this product and language; if not, create it
            product_detail = self.product.product_details.filter(language=language).first()
            
            if not product_detail:
                # Create a new product detail for the language if it doesn't exist
                name = self.product.name if language.name == 'English' else ''
                description = self.product.description if language.name == 'English' else ''
                tags = []  # Empty tags for non-provided languages

                ProductDetail.objects.create(
                    product=self.product,
                    language=language,
                    name=name,
                    description=description,
                    tags=tags
                )   


    def __str__(self):
        return self.name +' - '+ self.product.name

class ProductLinkVariant(BaseModel):
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='linked_variants')
    linked_variant = models.ForeignKey(ProductVariant, on_delete=models.CASCADE, related_name='linked_variant')
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = 'product_link_variants'
        unique_together = ('product', 'linked_variant')

class Collection(BaseModel):
    name        = models.CharField(max_length=255)
    description = models.TextField(null=True, blank=True)
    products    = models.ManyToManyField(Product, related_name='collections')
    is_active   = models.BooleanField(default=True)

    class Meta:
        db_table = 'collections'

    def __str__(self):
        return self.name
from django.db import models
from django.db.models.signals import post_save, post_delete, pre_delete, pre_save
from django.dispatch import receiver
from authz.models.tenant import BaseModel
from coreservice.storage_backends import CategoryImageStorage
from .models import TenantModel, BaseModel, ImageStorage
from products.utils.image_processing import process_webp_images, cleanup_images, cleanup_old_images


class Category(BaseModel):
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    image = models.ImageField(
        upload_to="Category/",
        storage=ImageStorage(),
        null=True,
        blank=True,
        help_text="Upload image file. Original stored with filename, WebP versions generated automatically."
    )
    is_active = models.BooleanField(default=True)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True)

    class Meta:
        db_table = 'product_categories'
        ordering = ['name']
        verbose_name_plural = 'Categories'
    
    def __str__(self):
        return self.name

    def to_typesense_dict(self):
        return {
            'id': str(self.id),
            'name': self.name,
            'description': self.description or '',
            'is_active': self.is_active,
            'image_url': self.image.url if self.image else '',
            'parent_id': str(self.parent.id) if self.parent else None,
        }

@receiver(pre_delete, sender=Category)
def delete_category_image_on_delete(sender, instance, **kwargs):
    if instance.image:
        try:
            instance.image.delete(save=False)
        except Exception as e:
            print(f"Could not delete image for category {instance.name}: {e}")
    cleanup_images(instance, 'Category')

@receiver(pre_save, sender=Category)
def delete_old_category_image_on_change(sender, instance, **kwargs):
    if not instance.pk:
        return
    try:
        old_instance = Category.objects.get(pk=instance.pk)
    except Category.DoesNotExist:
        return
    cleanup_old_images(old_instance, instance, 'Category')


@receiver(post_save, sender=Category)
def process_category_images_async(sender, instance, created, **kwargs):
    if instance.image and hasattr(instance.image, 'name'):
        process_webp_images(instance, 'Category', [(200, 200), (300, 300)])


def _upsert_to_typesense(instance, collection_name, entity_type):
    from products.typesense.client import get_typesense_client

    try:
        client = get_typesense_client()
        document = instance.to_typesense_dict()
        client.collections[collection_name].documents.upsert(document)
        print(f"{entity_type} {instance.id} indexed in Typesense")
        return True
    except Exception as e:
        print(f"Error indexing {entity_type.lower()} {instance.id} in Typesense: {e}")
        return False


def _delete_from_typesense(instance, collection_name, entity_type):
    from products.typesense.client import get_typesense_client

    try:
        client = get_typesense_client()
        client.collections[collection_name].documents[str(instance.id)].delete()
        print(f"{entity_type} {instance.id} deleted from Typesense")
        return True
    except Exception as e:
        print(f"Error deleting {entity_type.lower()} {instance.id} from Typesense: {e}")
        return False


def _bulk_update_typesense(objects, collection_name, entity_type, reason=""):
    from products.typesense.client import get_typesense_client

    if not objects.exists():
        return

    try:
        client = get_typesense_client()
        success_count = 0
        error_count = 0

        for obj in objects:
            try:
                document = obj.to_typesense_dict()
                client.collections[collection_name].documents.upsert(document)
                success_count += 1
            except Exception as e:
                error_count += 1
                print(f"Error updating {entity_type.lower()} {obj.id} in Typesense: {e}")

        if success_count > 0:
            print(f"Updated {success_count} {entity_type.lower()}(s) in Typesense{reason}")
        if error_count > 0:
            print(f"Failed to update {error_count} {entity_type.lower()}(s) in Typesense{reason}")

    except Exception as e:
        print(f"Error bulk updating {entity_type.lower()}s in Typesense{reason}: {e}")


@receiver(post_delete, sender=Category)
def delete_category_from_typesense_on_delete(sender, instance, **kwargs):
    _delete_from_typesense(instance, 'categories', 'Category')


class Brand(BaseModel):
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    image = models.ImageField(
        upload_to='Brand/',
        storage=ImageStorage(),
        null=True,
        blank=True,
        help_text="Upload image file. Original stored with filename, WebP versions generated automatically."
    )
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = 'brands'
        ordering = ['name']
        verbose_name_plural = 'Brands'
    
    def __str__(self):
        return self.name
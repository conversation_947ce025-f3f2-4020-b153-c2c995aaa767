# Generated by Django 5.2.3 on 2025-06-21 10:57

import coreservice.storage_backends
import django.db.models.deletion
import django_multitenant.mixins
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('authz', '0003_remove_user_roles_alter_companymaster_address_and_more'),
        ('products', '0003_remove_category_image_url_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='category',
            name='thumbnail_image',
            field=models.ImageField(blank=True, help_text='Upload image to be processed into WebP format with 200x200 and 300x300 sizes', null=True, storage=coreservice.storage_backends.AWSPublicMediaStorage(), upload_to='Category/'),
        ),
        migrations.AddField(
            model_name='product',
            name='thumbnail_image',
            field=models.ImageField(blank=True, help_text='Upload image to be processed into WebP format with 200x200 and 300x300 sizes', null=True, storage=coreservice.storage_backends.AWSPublicMediaStorage(), upload_to='Product/'),
        ),
        migrations.AddField(
            model_name='subcategory',
            name='thumbnail_image',
            field=models.ImageField(blank=True, help_text='Upload image to be processed into WebP format with 200x200 and 300x300 sizes', null=True, storage=coreservice.storage_backends.AWSPublicMediaStorage(), upload_to='Subcategory/'),
        ),
        migrations.CreateModel(
            name='ProductImage',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('image', models.ImageField(help_text='Upload image to be processed into WebP format with 600x600 and 700x700 sizes', storage=coreservice.storage_backends.AWSPublicMediaStorage(), upload_to='ProductImages/')),
                ('is_primary', models.BooleanField(default=False, help_text='Mark as primary image for the product')),
                ('display_order', models.PositiveIntegerField(default=0, help_text='Order in which images should be displayed')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
                ('facility', models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s_account', to='authz.facility')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='product_images', to='products.product')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Product Image',
                'verbose_name_plural': 'Product Images',
                'db_table': 'product_images',
                'ordering': ['display_order', 'id'],
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
    ]

# Generated by Django 5.2.3 on 2025-06-20 06:10

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('products', '0002_category_image_url_category_section_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='category',
            name='image_url',
        ),
        migrations.RemoveField(
            model_name='subcategory',
            name='image_url',
        ),
        migrations.AddField(
            model_name='category',
            name='thumbnail_url',
            field=models.URLField(default=1),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='product',
            name='colour',
            field=models.CharField(blank=True, max_length=64, null=True),
        ),
        migrations.AddField(
            model_name='subcategory',
            name='thumbnail_url',
            field=models.CharField(default=1, max_length=512),
            preserve_default=False,
        ),
        migrations.Alter<PERSON>ield(
            model_name='product',
            name='display_alias',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.<PERSON><PERSON><PERSON>ield(
            model_name='product',
            name='image_url',
            field=models.JSO<PERSON>ield(blank=True, default=list, null=True),
        ),
    ]
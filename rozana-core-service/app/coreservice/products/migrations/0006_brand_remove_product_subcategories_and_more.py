# Generated by Django 5.2.4 on 2025-08-05 10:41

import django.db.models.deletion
import products.models.models
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('products', '0005_alter_productimage_options_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Brand',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('image', models.ImageField(blank=True, help_text='Upload image file. Original stored with filename, WebP versions generated automatically.', null=True, storage=products.models.models.ImageStorage(), upload_to='Brand/')),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name_plural': 'Brands',
                'db_table': 'brands',
                'ordering': ['name'],
            },
        ),
        migrations.RemoveField(
            model_name='product',
            name='subcategories',
        ),
        migrations.RemoveField(
            model_name='category',
            name='code',
        ),
        migrations.RemoveField(
            model_name='category',
            name='section',
        ),
        migrations.RemoveField(
            model_name='category',
            name='thumbnail_image',
        ),
        migrations.RemoveField(
            model_name='category',
            name='thumbnail_url',
        ),
        migrations.AddField(
            model_name='category',
            name='image',
            field=models.ImageField(blank=True, help_text='Upload image file. Original stored with filename, WebP versions generated automatically.', null=True, storage=products.models.models.ImageStorage(), upload_to='Category/'),
        ),
        migrations.AddField(
            model_name='category',
            name='parent',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='products.category'),
        ),
        migrations.AddField(
            model_name='product',
            name='Categories',
            field=models.ManyToManyField(related_name='products', to='products.category'),
        ),
        migrations.DeleteModel(
            name='Subcategory',
        ),
    ]

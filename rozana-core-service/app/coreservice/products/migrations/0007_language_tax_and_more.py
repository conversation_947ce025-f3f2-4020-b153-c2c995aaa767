# Generated by Django 5.2.4 on 2025-08-05 11:05

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('products', '0006_brand_remove_product_subcategories_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Language',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
            ],
        ),
        migrations.CreateModel(
            name='Tax',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('percentage', models.DecimalField(decimal_places=2, max_digits=5)),
                ('cgst_percentage', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('sgst_percentage', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('igst_percentage', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('cess_percentage', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'Tax',
                'verbose_name_plural': 'Taxes',
                'db_table': 'taxes',
            },
        ),
        migrations.RenameField(
            model_name='product',
            old_name='display_alias',
            new_name='additional_details',
        ),
        migrations.AlterUniqueTogether(
            name='product',
            unique_together=set(),
        ),
        migrations.AddField(
            model_name='product',
            name='category',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='products', to='products.category'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='product',
            name='image',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='product',
            name='is_published',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='product',
            name='slug',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='product',
            name='top_product',
            field=models.BooleanField(default=False),
        ),
        migrations.AlterField(
            model_name='product',
            name='brand',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='products', to='products.brand'),
        ),
        migrations.AlterField(
            model_name='product',
            name='image_url',
            field=models.CharField(blank=True, max_length=512, null=True),
        ),
        migrations.AlterField(
            model_name='product',
            name='sku',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='productimage',
            name='image',
            field=models.TextField(default=1),
            preserve_default=False,
        ),
        migrations.CreateModel(
            name='Collection',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('products', models.ManyToManyField(related_name='collections', to='products.product')),
            ],
            options={
                'db_table': 'collections',
            },
        ),
        migrations.CreateModel(
            name='ProductVariant',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255)),
                ('slug', models.CharField(blank=True, max_length=255, null=True)),
                ('sku', models.CharField(blank=True, max_length=255, null=True)),
                ('price', models.FloatField(blank=True, null=True)),
                ('mrp', models.FloatField(blank=True, null=True)),
                ('ssp', models.FloatField(blank=True, null=True)),
                ('ean_number', models.BigIntegerField(blank=True, null=True)),
                ('hsn_code', models.TextField(blank=True, null=True)),
                ('size', models.CharField(blank=True, max_length=255, null=True)),
                ('color', models.CharField(blank=True, max_length=255, null=True)),
                ('weight', models.CharField(blank=True, max_length=255, null=True)),
                ('net_qty', models.CharField(blank=True, max_length=255, null=True)),
                ('packaging_type', models.CharField(blank=True, max_length=255, null=True)),
                ('image', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='variants', to='products.product')),
            ],
            options={
                'db_table': 'variants',
                'unique_together': {('name', 'sku')},
            },
        ),
        migrations.RemoveField(
            model_name='product',
            name='Categories',
        ),
        migrations.RemoveField(
            model_name='product',
            name='attributes',
        ),
        migrations.RemoveField(
            model_name='product',
            name='colour',
        ),
        migrations.RemoveField(
            model_name='product',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='product',
            name='facility',
        ),
        migrations.RemoveField(
            model_name='product',
            name='price',
        ),
        migrations.RemoveField(
            model_name='product',
            name='thumbnail_image',
        ),
        migrations.RemoveField(
            model_name='product',
            name='thumbnail_url',
        ),
        migrations.RemoveField(
            model_name='product',
            name='updated_by',
        ),
        migrations.RemoveField(
            model_name='product',
            name='weight',
        ),
        migrations.RemoveField(
            model_name='product',
            name='weight_unit',
        ),
        migrations.CreateModel(
            name='ProductDetail',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(blank=True, max_length=255, null=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('tags', models.JSONField(blank=True, default=list, null=True)),
                ('language', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.language')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='product_details', to='products.product')),
            ],
            options={
                'unique_together': {('product', 'language')},
            },
        ),
        migrations.CreateModel(
            name='ProductLinkVariant',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='linked_variants', to='products.product')),
                ('linked_variant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='linked_variant', to='products.productvariant')),
            ],
            options={
                'db_table': 'product_link_variants',
                'unique_together': {('product', 'linked_variant')},
            },
        ),
    ]

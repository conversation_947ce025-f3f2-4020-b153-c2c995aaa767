import csv
import os
import re
import random
from decimal import Decimal
from django.core.management.base import BaseCommand
from django.db import transaction
from django.utils.text import slugify
from authz.models.facility import Facility
from products.models.category import Category
from products.models.product import Product
from products.typesense.client import get_typesense_client, check_typesense_health
import requests
import time
import logging
import pandas as pd 

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Generate sample data with categories, subcategories, and products'

    def add_arguments(self, parser):
        parser.add_argument(
            '--csv-file',
            type=str,
            default='insta_products_latest_2.csv',
            help='Path to the CSV file with product data'
        )
        parser.add_argument(
            '--limit',
            type=int,
            default=200,
            help='Limit the number of products to import (0 for all)'
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force recreation of data even if it already exists'
        )

    def handle(self, *args, **options):
        csv_file = options['csv_file']
        product_limit = options['limit']
        force = options['force']
        
        # Check if the CSV file exists
        if not os.path.exists(csv_file):
            self.stdout.write(self.style.ERROR(f'CSV file not found: {csv_file}'))
            return
        
        # Check if data already exists
        if not force and Category.objects.exists():
            self.stdout.write(self.style.WARNING(
                'Categories already exist. Use --force to recreate data.'
            ))
            return
            
        # Check if Typesense is healthy
        check_typesense_health(self)
        
        # Get the facility
        try:
            facility = Facility.objects.first()
            if not facility:
                self.stdout.write(self.style.ERROR('No facility found. Please create a facility first.'))
                return
            self.stdout.write(self.style.SUCCESS(f'Using facility: {facility.name} (ID: {facility.id})'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error getting facility: {e}'))
            return
        
        categories_dict = {}
        chunk_size = 10000
        all_rows = []

        try:
            reader = pd.read_csv(
                csv_file,
                usecols=[
                    'name','brand_id','section','carousel_images','store_price','mrp','discount','in_stock','total_quantity_in_stock','weight_in_grams','store_id',
                    'category_id','variation_id','variation_display_name','quantity','max_allowed_quantity',
                    'category_name', 'category_url', 'sub_category', 'sub_category_url',
                    'brand_name', 'price', 'sku', 'image_url', 'quantity'
                ],
                chunksize=chunk_size,
                encoding='utf-8',
                low_memory=False
            )

            for chunk in reader:
                chunk = chunk.fillna('')
                for col in chunk.columns:
                    chunk[col] = chunk[col].astype(str).str.strip('"')

                all_rows.extend(chunk.to_dict('records'))
                if product_limit > 0 and len(all_rows) >= product_limit:
                    all_rows = all_rows[:product_limit]
                    break

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error reading CSV: {e}'))
            return
    
        # Generate data
        with transaction.atomic():
            if force:
                # Clear existing data
                self.stdout.write('Clearing existing data...')
                Product.objects.all().delete()
                Subcategory.objects.all().delete()
                Category.objects.all().delete()
            
            # Create categories and subcategories
            self.stdout.write('Creating categories and subcategories...')
            category_map = {}  # Maps category name to Category object
            subcategory_map = {}  # Maps (category_name, subcategory_name) to Subcategory object
                
            for i, row in enumerate(all_rows):
                category_name = row['category_name']
                subcategory_name = row['sub_category']
                category_url = row['category_url']
                subcategory_url = row['sub_category_url']

                if not category_name:
                    continue

                if category_name not in categories_dict:
                    categories_dict[category_name] = {
                        'category_url': category_url,
                        'subcategories': set(),
                        'subcategory_urls': set()
                    }
                if subcategory_name and subcategory_name not in categories_dict[category_name]['subcategories']:
                    categories_dict[category_name]['subcategories'].add(subcategory_name)
                    categories_dict[category_name]['subcategory_urls'].add(subcategory_url)

            for i, (cat_name, cat_data) in enumerate(categories_dict.items()):
                cat_code = f"CAT-{i+1:03d}"
                category = Category.objects.create(
                    name=cat_name,
                    description=f"Products in the {cat_name} category",
                    code=cat_code,
                    is_active=True,
                    thumbnail_url=cat_data['category_url']
                )
                category_map[cat_name] = category

                for j, (subcat_name, subcat_url) in enumerate(zip(cat_data['subcategories'], cat_data['subcategory_urls'])):
                    subcat_code = f"{cat_code}-SUB-{j+1:02d}"
                    subcategory = Subcategory.objects.create(
                        name=subcat_name,
                        description=f"{subcat_name} products in the {cat_name} category",
                        code=subcat_code,
                        category=category,
                        is_active=True,
                        thumbnail_url=subcat_url
                    )
                    subcategory_map[(cat_name, subcat_name)] = subcategory

            # Create products
            self.stdout.write('Creating products...')
            products_created = 0
            products_skipped = 0

            for i, row in enumerate(all_rows):
                try:
                    name = row['name'].strip()
                    price_str = row['price'].strip()
                    quantity = row.get('quantity', '').strip()
                    brand = row.get('brand_name', '').strip()

                    try:
                        price = Decimal(price_str)
                    except:
                        match = re.search(r'\d+(\.\d+)?', price_str)
                        price = Decimal(match.group(0)) if match else Decimal('0.00')

                    weight = Decimal('0.5')
                    weight_unit = 'kg'
                    weight_match = re.search(r'(\d+(\.\d+)?)\s*(kg|g|lb|oz)', quantity, re.IGNORECASE)
                    if weight_match:
                        weight = Decimal(weight_match.group(1))
                        weight_unit = weight_match.group(3).lower()

                    sku = row['sku'].strip()
                    sku = f"SKU-{sku}-{i+1:04d}-{random.randint(1000, 9999)}"

                    cat_name = row['category_name'].strip('"')
                    subcat_name = row['sub_category'].strip('"')

                    if cat_name not in category_map or (cat_name, subcat_name) not in subcategory_map:
                        products_skipped += 1
                        continue

                    thumbnail_url = row['image_url'].strip('"')
                    if not thumbnail_url:
                        thumbnail_url = f"https://picsum.photos/seed/{sku}/300/300"

                    carousel_images = row['carousel_images'].strip()
                    if carousel_images:
                        # Split by '|' and clean up URLs, filter out empty strings
                        url_list = [url.strip() for url in carousel_images.split('|') if url.strip()]
                    else:
                        # Always use empty list for consistency with ArrayField
                        url_list = []
                    product = Product.objects.create(
                        name=name,
                        description=f"{name} - {quantity}",
                        sku=sku,
                        price=price,
                        brand=brand,
                        is_active=True,
                        image_url=url_list,
                        weight=weight,
                        weight_unit=weight_unit,
                        thumbnail_url=thumbnail_url,
                        facility=facility
                    )

                    subcategory = subcategory_map[(cat_name, subcat_name)]
                    product.subcategories.add(subcategory)
                    products_created += 1

                    if products_created % 20 == 0:
                        self.stdout.write(f'  Created {products_created} products')

                except Exception as e:
                    self.stdout.write(self.style.ERROR(f'  Error creating product from row {i+1}: {e}'))
                    products_skipped += 1

        self.stdout.write(self.style.SUCCESS(
            f'Successfully created {len(category_map)} categories, '
            f'{len(subcategory_map)} subcategories, and '
            f'{products_created} products ({products_skipped} skipped)'
        ))
    #     self.index_categories_in_typesense()
    #     self.index_subcategories_in_typesense()

    # def index_categories_in_typesense(self):
    #     """Index all categories in Typesense"""
    #     self.stdout.write('Indexing categories in Typesense...')
    #     try:
    #         client = get_typesense_client()
            
    #         # Check if collection exists, if not create it
    #         try:
    #             client.collections['categories'].retrieve()
    #             self.stdout.write('Categories collection exists in Typesense')
    #         except Exception:
    #             # Create the collection
    #             schema = {
    #                 'name': 'categories',
    #                 'fields': [
    #                     {'name': 'id', 'type': 'string'},
    #                     {'name': 'name', 'type': 'string'},
    #                     {'name': 'description', 'type': 'string', 'optional': True},
    #                     {'name': 'code', 'type': 'string'},
    #                     {'name': 'is_active', 'type': 'bool'},
    #                     {'name': 'thumbnail_url', 'type': 'string'},
    #                     {'name': 'section', 'type': 'string', 'facet': True},
    #                 ],
    #             }
    #             client.collections.create(schema)
    #             self.stdout.write('Created categories collection in Typesense')
            
    #         # Index all categories
    #         categories = Category.objects.all()
    #         total_categories = categories.count()
    #         indexed_count = 0
    #         error_count = 0
            
    #         for category in categories:
    #             try:
    #                 document = category.to_typesense_dict()
    #                 client.collections['categories'].documents.upsert(document)
    #                 indexed_count += 1
                    
    #                 if indexed_count % 20 == 0 or indexed_count == total_categories:
    #                     self.stdout.write(f'Indexed {indexed_count}/{total_categories} categories')
    #             except Exception as e:
    #                 error_count += 1
    #                 self.stdout.write(self.style.ERROR(f'Error indexing category {category.id}: {e}'))
            
    #         self.stdout.write(self.style.SUCCESS(
    #             f'Indexing complete: {indexed_count} categories indexed successfully, {error_count} errors'
    #         ))
    #     except Exception as e:
    #         self.stdout.write(self.style.ERROR(f'Error during Typesense indexing: {e}'))

    # def index_subcategories_in_typesense(self):
    #     """Index all subcategories in Typesense"""
    #     self.stdout.write('Indexing subcategories in Typesense...')
    #     try:
    #         client = get_typesense_client()
            
    #         # Check if collection exists, if not create it
    #         try:
    #             client.collections['subcategories'].retrieve()
    #             self.stdout.write('Subcategories collection exists in Typesense')
    #         except Exception:
    #             # Create the collection
    #             schema = {
    #                 'name': 'subcategories',
    #                 'fields': [
    #                     {'name': 'id', 'type': 'string'},
    #                     {'name': 'name', 'type': 'string'},
    #                     {'name': 'description', 'type': 'string', 'optional': True},
    #                     {'name': 'code', 'type': 'string'},
    #                     {'name': 'category_id', 'type': 'int32', 'facet': True},
    #                     {'name': 'category_name', 'type': 'string', 'facet': True},
    #                     {'name': 'is_active', 'type': 'bool'},
    #                     {'name': 'thumbnail_url', 'type': 'string'},
    #                 ],
    #             }
    #             client.collections.create(schema)
    #             self.stdout.write('Created subcategories collection in Typesense')
            
    #         # Index all subcategories
    #         subcategories = Subcategory.objects.all()
    #         total_subcategories = subcategories.count()
    #         indexed_count = 0
    #         error_count = 0
            
    #         for subcategory in subcategories:
    #             try:
    #                 document = subcategory.to_typesense_dict()
    #                 client.collections['subcategories'].documents.upsert(document)
    #                 indexed_count += 1
                    
    #                 if indexed_count % 20 == 0 or indexed_count == total_subcategories:
    #                     self.stdout.write(f'Indexed {indexed_count}/{total_subcategories} subcategories')
    #             except Exception as e:
    #                 error_count += 1
    #                 self.stdout.write(self.style.ERROR(f'Error indexing subcategory {subcategory.id}: {e}'))
            
    #         self.stdout.write(self.style.SUCCESS(
    #             f'Indexing complete: {indexed_count} subcategories indexed successfully, {error_count} errors'
    #         ))
    #     except Exception as e:
    #         self.stdout.write(self.style.ERROR(f'Error during Typesense indexing: {e}'))

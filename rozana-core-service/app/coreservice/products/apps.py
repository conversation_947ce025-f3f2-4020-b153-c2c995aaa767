from django.apps import AppConfig
import time
import threading


class ProductsConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'products'
    
    def ready(self):
        """Initialize Typesense when the app is ready"""
        # Import here to avoid circular imports
        from products.typesense.client import initialize_typesense_schema
        
        # Start Typesense initialization in a background thread with retry logic
        def initialize_with_retry():
            max_retries = 10
            retry_delay = 5  # seconds
            
            for attempt in range(max_retries):
                try:
                    print(f"Attempting to initialize Typesense schema (attempt {attempt + 1}/{max_retries})")
                    initialize_typesense_schema()
                    print("Successfully initialized Typesense schema!")
                    return
                except Exception as e:
                    print(f"Error initializing Typesense schema: {e}")
                    if attempt < max_retries - 1:
                        print(f"Retrying in {retry_delay} seconds...")
                        time.sleep(retry_delay)
                    else:
                        print("Max retries reached. Typesense initialization failed.")
        
        # Start initialization in a background thread to not block app startup
        thread = threading.Thread(target=initialize_with_retry)
        thread.daemon = True
        thread.start()


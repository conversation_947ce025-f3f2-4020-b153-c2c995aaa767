from django.contrib import admin
from products.models import Product, Category, ProductImage, Brand


@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'is_active', 'has_image')
    list_filter = ('is_active',)
    search_fields = ('name', 'description')
    readonly_fields = ('creation_date', 'updation_date')

    fieldsets = (
        (None, {
            'fields': ('name', 'description', 'parent')
        }),
        ('Images', {
            'fields': ('image',),
            'description': 'Upload an image file (stored with original filename, WebP versions generated automatically).'
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('Timestamps', {
            'fields': ('creation_date', 'updation_date'),
            'classes': ('collapse',)
        }),
    )

    def has_image(self, obj):
        return bool(obj.image)
    has_image.boolean = True
    has_image.short_description = 'Has Image'


class ProductImageInline(admin.TabularInline):
    """
    Inline admin for ProductImage model to allow multiple image uploads per product
    """
    model = ProductImage
    extra = 1
    max_num = 10
    fields = ('image', 'priority', 'alt_text', 'is_primary')


@admin.register(ProductImage)
class ProductImageAdmin(admin.ModelAdmin):
    """
    Standalone admin for ProductImage model (optional, for advanced management)
    """
    list_display = ('product', 'priority', 'is_primary', 'alt_text')
    list_filter = ('is_primary', 'product__brand')
    search_fields = ('product__name', 'product__sku', 'alt_text')


@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = ('name', 'sku', 'brand', 'category', 'is_active', 'has_thumbnail_image', 'image_count')
    list_filter = ('is_active', 'brand', 'category')
    search_fields = ('name', 'sku', 'description')
    readonly_fields = ('creation_date', 'updation_date')
    inlines = [ProductImageInline]

    fieldsets = (
        (None, {
            'fields': ('name', 'description', 'sku', 'slug')
        }),
        ('Categorization', {
            'fields': ('category', 'brand', 'tags')
        }),
        ('Product Details', {
            'fields': ('top_product', 'additional_details')
        }),
        ('Images', {
            'fields': ('image_url', 'image'),
            'description': 'Image URL or image text field.',
        }),
        ('Status', {
            'fields': ('is_active', 'is_published')
        }),
        ('Timestamps', {
            'fields': ('creation_date', 'updation_date'),
            'classes': ('collapse',)
        }),
    )

    def has_thumbnail_image(self, obj):
        return bool(obj.image_url or obj.image)
    has_thumbnail_image.boolean = True
    has_thumbnail_image.short_description = 'Has Image'

    def image_count(self, obj):
        return obj.product_images.count()
    image_count.short_description = "Images"


@admin.register(Brand)
class BrandAdmin(admin.ModelAdmin):
    list_display = ('name', 'is_active', 'has_image')
    list_filter = ('is_active',)
    search_fields = ('name', 'description')
    readonly_fields = ('creation_date', 'updation_date')

    fieldsets = (
        (None, {
            'fields': ('name', 'description')
        }),
        ('Images', {
            'fields': ('image',),
            'description': 'Upload an image file (stored with original filename, WebP versions generated automatically) OR provide a direct URL.'
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('Timestamps', {
            'fields': ('creation_date', 'updation_date'),
            'classes': ('collapse',)
        }),
    )

    def has_image(self, obj):
        return bool(obj.image)
    has_image.boolean = True
    has_image.short_description = 'Has Image'

from datetime import datetime
from decimal import Decimal, InvalidOperation
import re
import pytz
from django.utils import timezone
from products.models.product import Product
from authz.models.facility import Facility


class OrderValidator:
    """Base validator class for order validation"""
    
    def __init__(self, data):
        self.data = data
        self.errors = {}
    
    def is_valid(self):
        """Run all validations and return True if valid"""
        self.validate()
        return len(self.errors) == 0
    
    def validate(self):
        """Override this method in subclasses"""
        pass


class CustomerValidator(OrderValidator):
    """Validator for customer data"""
    
    def validate(self):
        customer = self.data.get('customer', {})
        
        # Check if customer data is provided
        if not customer:
            self.errors['customer'] = 'Customer information is required'
            return
        
        # Validate required fields
        required_fields = ['customer_name', 'city', 'address_line_1', 'state', 'pincode']
        for field in required_fields:
            if not customer.get(field):
                self.errors[f'customer.{field}'] = f'{field.replace("_", " ").title()} is required'
        
        # Validate pincode format
        pincode = customer.get('pincode')
        if pincode and (not isinstance(pincode, int) or len(str(pincode)) != 6):
            self.errors['customer.pincode'] = 'Pincode must be a 6-digit number'


class OrderItemValidator(OrderValidator):
    """Validator for order items"""
    
    def validate(self):
        items = self.data.get('items', [])
        
        # Check if items are provided
        if not items:
            self.errors['items'] = 'At least one item is required'
            return
        
        # Validate each item
        for i, item in enumerate(items):
            # Check required fields
            required_fields = ['sku', 'quantity', 'mrp', 'sale_price_gst']
            for field in required_fields:
                if field not in item:
                    self.errors[f'items[{i}].{field}'] = f'{field.upper()} is required'
            
            # Validate SKU exists
            sku = item.get('sku')
            if sku and not self._validate_sku_exists(sku):
                self.errors[f'items[{i}].sku'] = f'Product with SKU {sku} not found'
            
            # Validate quantity
            quantity = item.get('quantity')
            if quantity is not None:
                if not isinstance(quantity, int) or quantity <= 0:
                    self.errors[f'items[{i}].quantity'] = 'Quantity must be a positive integer'
            
            # Validate price fields
            self._validate_price_field(item, 'mrp', i)
            self._validate_price_field(item, 'sale_price_gst', i)
            self._validate_price_field(item, 'discount_amount', i)
            
            # Validate discount amount is less than or equal to MRP
            if 'mrp' in item and 'discount_amount' in item:
                try:
                    mrp = Decimal(str(item['mrp']))
                    discount = Decimal(str(item['discount_amount']))
                    if discount > mrp:
                        self.errors[f'items[{i}].discount_amount'] = 'Discount amount cannot be greater than MRP'
                except (ValueError, TypeError, InvalidOperation):
                    pass  # Already handled in _validate_price_field
    
    def _validate_sku_exists(self, sku):
        """Check if a product with the given SKU exists"""
        return Product.objects.filter(sku=sku).exists()
    
    def _validate_price_field(self, item, field, index):
        """Validate a price field is a valid decimal"""
        if field in item:
            try:
                value = Decimal(str(item[field]))
                if value < 0:
                    self.errors[f'items[{index}].{field}'] = f'{field.upper()} cannot be negative'
            except (ValueError, TypeError, InvalidOperation):
                self.errors[f'items[{index}].{field}'] = f'{field.upper()} must be a valid number'


class OrderDetailsValidator(OrderValidator):
    """Validator for order details"""
    
    def validate(self):
        # Validate order reference
        if not self.data.get('order_reference'):
            self.errors['order_reference'] = 'Order reference is required'
        elif not re.match(r'^[A-Za-z0-9-_]+$', self.data.get('order_reference')):
            self.errors['order_reference'] = 'Order reference contains invalid characters'
        
        # Validate facility
        facility = self.data.get('facility')
        if not facility:
            self.errors['facility'] = 'Facility is required'
        elif not self._validate_facility_exists(facility):
            self.errors['facility'] = f'Facility with code {facility} not found'
        
        # Validate order type
        order_type = self.data.get('order_type')
        if order_type and order_type not in ['Express', 'Standard', 'Scheduled']:
            self.errors['order_type'] = 'Invalid order type. Must be Express, Standard, or Scheduled'
        
        # Validate time slots
        self._validate_time_slots()
    
    def _validate_facility_exists(self, code):
        """Check if a facility with the given code exists"""
        return Facility.objects.filter(code=code).exists()
    
    def _validate_time_slots(self):
        """Validate time slots and promised time"""
        slot_from = self.data.get('slot_from')
        slot_to = self.data.get('slot_to')
        promised_time = self.data.get('promised_time')
        
        # Validate time format for slots
        if slot_from and not self._is_valid_time_format(slot_from):
            self.errors['slot_from'] = 'Invalid time format. Use HH:MM:SS'
        
        if slot_to and not self._is_valid_time_format(slot_to):
            self.errors['slot_to'] = 'Invalid time format. Use HH:MM:SS'
        
        # Validate slot_from is before slot_to
        if slot_from and slot_to and self._is_valid_time_format(slot_from) and self._is_valid_time_format(slot_to):
            if slot_from >= slot_to:
                self.errors['slot_to'] = 'Slot end time must be after slot start time'
        
        # Validate promised time format and is in future
        if promised_time:
            try:
                promised_dt = datetime.strptime(promised_time, '%Y-%m-%d %H:%M:%S')
                if promised_dt.replace(tzinfo=pytz.UTC) <= timezone.now():
                    self.errors['promised_time'] = 'Promised time must be in the future'
            except ValueError:
                self.errors['promised_time'] = 'Invalid datetime format. Use YYYY-MM-DD HH:MM:SS'
    
    def _is_valid_time_format(self, time_str):
        """Check if a string is in valid time format (HH:MM:SS)"""
        try:
            datetime.strptime(time_str, '%H:%M:%S')
            return True
        except ValueError:
            return False


class OrderValidator(OrderValidator):
    """Main validator for the entire order"""
    
    def validate(self):
        # Validate customer data
        customer_validator = CustomerValidator(self.data)
        customer_validator.validate()
        self.errors.update(customer_validator.errors)
        
        # Validate order items
        item_validator = OrderItemValidator(self.data)
        item_validator.validate()
        self.errors.update(item_validator.errors)
        
        # Validate order details
        details_validator = OrderDetailsValidator(self.data)
        details_validator.validate()
        self.errors.update(details_validator.errors)

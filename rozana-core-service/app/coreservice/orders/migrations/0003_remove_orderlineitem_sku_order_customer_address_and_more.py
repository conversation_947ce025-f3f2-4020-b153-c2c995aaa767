# Generated by Django 5.2.2 on 2025-06-10 03:53

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0002_orderlineitem_sku'),
        ('products', '0001_initial'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='orderlineitem',
            name='sku',
        ),
        migrations.AddField(
            model_name='order',
            name='customer_address',
            field=models.TextField(default='Delhi'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='order',
            name='customer_name',
            field=models.Char<PERSON>ield(default='Cus1', max_length=255),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='order',
            name='customer_reference',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='order',
            name='order_type',
            field=models.CharField(default='Standard', max_length=50),
        ),
        migrations.AddField(
            model_name='order',
            name='promised_time',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='order',
            name='slot_from',
            field=models.CharField(blank=True, max_length=8, null=True),
        ),
        migrations.AddField(
            model_name='order',
            name='slot_to',
            field=models.CharField(blank=True, max_length=8, null=True),
        ),
        migrations.AddField(
            model_name='orderlineitem',
            name='discount_amount',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10),
        ),
        migrations.AddField(
            model_name='orderlineitem',
            name='mrp',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='orderlineitem',
            name='product',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.PROTECT, related_name='order_items', to='products.product'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='order',
            name='status',
            field=models.CharField(default='Pending', max_length=100),
        ),
    ]

"""
Django management command to test Sentry integration
"""
from django.core.management.base import BaseCommand
from django.conf import settings
from coreservice.sentry_utils import test_sentry_integration


class Command(BaseCommand):
    help = 'Test Sentry integration by sending test messages and errors'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force test even in DEBUG mode',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Testing Sentry integration...'))
        
        # Check if Sentry is configured
        if not hasattr(settings, 'SENTRY_DSN') or not settings.SENTRY_DSN:
            self.stdout.write(
                self.style.ERROR('Sentry DSN is not configured. Please set ENV_SENTRY_DSN environment variable.')
            )
            return
            
        if settings.DEBUG and not options['force']:
            self.stdout.write(
                self.style.WARNING(
                    'DEBUG mode is enabled. Sentry is disabled in DEBUG mode. '
                    'Use --force to test anyway or set DEBUG=False.'
                )
            )
            return
            
        # Test Sentry integration
        success = test_sentry_integration()
        
        if success:
            self.stdout.write(
                self.style.SUCCESS(
                    'Sentry test completed! Check your Sentry dashboard for test messages.'
                )
            )
        else:
            self.stdout.write(
                self.style.ERROR('Sentry test failed. Check your configuration.')
            )
            
        # Display current configuration
        self.stdout.write('\nCurrent Sentry Configuration:')
        self.stdout.write(f'  DSN: {settings.SENTRY_DSN[:50]}...' if settings.SENTRY_DSN else '  DSN: Not set')
        self.stdout.write(f'  Environment: {getattr(settings, "SENTRY_ENVIRONMENT", "Not set")}')
        self.stdout.write(f'  Release: {getattr(settings, "ENV_SENTRY_RELEASE", "Not set")}')
        self.stdout.write(f'  DEBUG: {settings.DEBUG}')

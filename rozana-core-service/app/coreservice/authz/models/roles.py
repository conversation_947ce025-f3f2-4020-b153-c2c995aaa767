# from authz.models.tenant import BaseModel
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth.models import Group #, Permission



# class Role(BaseModel):
#     name = models.CharField(max_length=100)
#     code_name = models.CharField(max_length=150)
#     description = models.TextField()
#     company = models.ForeignKey('CompanyMaster', on_delete=models.CASCADE, related_name='roles_company', null=True, blank=True)
#     permissions = models.ManyToManyField(
#         Permission,
#         verbose_name=_("permissions"),
#         blank=True,
#     )

#     def __str__(self):
#         return self.name


class CompanyGroup(Group):
    """
    Extends Django's built-in Group model with company association
    and additional fields.
    """
    company = models.ForeignKey('CompanyMaster', on_delete=models.CASCADE, related_name='company_groups', null=True, blank=True)
    description = models.TextField(blank=True)
    code_name = models.Char<PERSON>ield(max_length=150, blank=True)
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('company group')
        verbose_name_plural = _('company groups')
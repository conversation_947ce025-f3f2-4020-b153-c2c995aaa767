from django.db import models
from django.contrib.auth.models import AbstractUser
from django.utils.translation import gettext_lazy as _
from authz.models.tenant import BaseModel


class User(AbstractUser):
    MASTER = 'master'
    MANAGER = 'manager'
    USER_ROLES = [
        (MASTER, 'Master'),
        (MANAGER, 'Manager'),
    ]

    role = models.CharField(
        max_length=20,
        choices=USER_ROLES,
        default=MANAGER,
    )

    def __str__(self):
        return f"{self.first_name} {self.last_name} ({self.email})"


class UserDetails(BaseModel):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='user_details')
    address = models.CharField(max_length=100)
    phone = models.CharField(max_length=15)
    date_of_birth = models.DateField(null=True, blank=True)

    def __str__(self):
        return self.user.username


class UserFacilityMapping(BaseModel):

    # STATUS is a tuple of choices for the status field.
    # The first element of each tuple is the value that will be stored in the database,
    # and the second element is the human-readable name for the choice.
    # For example, if the status field is set to 0, the value 'Active' will be displayed to the user.
    STATUS = (
        (0, _('Active')),
        (1, _('Inactive')),
    )

    user = models.ForeignKey('User', on_delete=models.CASCADE, related_name='user_facility_mappings')
    facility = models.ForeignKey('Facility', on_delete=models.CASCADE, related_name='user_facility_mappings')
    status = models.IntegerField(choices=STATUS)

    def __str__(self):
        return f"{self.user} - {self.facility}"
        

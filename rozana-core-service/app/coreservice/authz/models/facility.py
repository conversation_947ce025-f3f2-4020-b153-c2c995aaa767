from django.db import models
from products.models.product import ProductVariant, Tax
from django_multitenant.mixins import TenantModelMixin
from authz.models.tenant import BaseModel


class Cluster(BaseModel):
    name        = models.CharField(max_length=255)
    latitude    = models.TextField(blank=True, null=True)
    longitude   = models.TextField(blank=True, null=True)
    region      = models.CharField(max_length=255, blank=True, null=True)
    facilities  = models.ManyToManyField('Facility', related_name='clusters', blank=True)
    is_active   = models.BooleanField(default=True)

    class Meta:
        db_table = 'clusters'
        unique_together = ('name',)

    def __str__(self):
        return self.name


class Facility(TenantModelMixin, BaseModel):

    facility_types = [
        ('STORE', 'Store'),
        ('WAREHOUSE', 'Warehouse'),
        ('DISTRIBUTION_CENTER', 'Distribution Center'),
    ]

    tenant_id = 'id'
    name = models.Char<PERSON>ield(max_length=100)
    code = models.Char<PERSON>ield(max_length=100)
    description = models.TextField()
    facility_type = models.CharField(max_length=100, choices=facility_types)
    company = models.ForeignKey('CompanyMaster', on_delete=models.CASCADE, related_name='facilities_company')
    address = models.CharField(max_length=100)
    city = models.CharField(max_length=100)
    state = models.CharField(max_length=100)
    country = models.CharField(max_length=100)
    pincode = models.CharField(max_length=100)
    latitude = models.CharField(max_length=100)
    longitude = models.CharField(max_length=100)


    def __str__(self) -> str:
        return self.name


class FacilityInventory(BaseModel):

    facility        = models.ForeignKey(Facility, on_delete=models.CASCADE, related_name='facility_inventories')
    product_variant = models.ForeignKey(ProductVariant, on_delete=models.CASCADE, related_name='facility_inventories')
    stock           = models.IntegerField(default=0)
    tax             = models.ForeignKey(Tax, on_delete=models.CASCADE, blank=True, null=True)
    price           = models.FloatField(default=0.0)
    csp             = models.FloatField(default=0.0)
    cust_discount   = models.IntegerField(blank=True, null=True)
    max_purchase_limit      = models.IntegerField(blank=True, null=True)
    outofstock_threshold    = models.IntegerField(blank=True, null=True)
    status          = models.CharField(max_length=100, blank=True, null=True)
    is_active       = models.BooleanField(default=True)

    class Meta:
        db_table = 'facility_inventories'
        unique_together = ('facility', 'product_variant')

    def save(self, *args, **kwargs):
        # If price or csp is not provided, set them to 0
        if self.price is None:
            self.price = 0.0
        if self.csp is None:
            self.csp = 0.0

        # Calculate cust_discount if it's not already set
        if self.cust_discount is None:
            self.cust_discount = int(self.price - self.csp)  # Calculate discount

        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.facility.name} - {self.product_variant.name}"

from authz.models.tenant import BaseModel
from django.db import models
from django.utils.translation import gettext_lazy as _
from coreservice.storage_backends import PrivateMediaStorage


class CompanyMaster(BaseModel):
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=100)
    company_logo = models.ImageField(upload_to='images/companies/',  storage=PrivateMediaStorage(), null=True,  default='')
    address = models.CharField(max_length=255, default='', blank=True)
    city = models.CharField(max_length=100, default='', blank=True)
    state = models.CharField(max_length=100, default='', blank=True)
    country = models.CharField(max_length=100, default='', blank=True)
    pincode = models.CharField(max_length=100, default='', blank=True)
    phone = models.CharField(max_length=20, default='', blank=True)
    email = models.CharField(max_length=100, default='', blank=True)
    pan_number = models.CharField(max_length=20, default='', blank=True)
    gst_number = models.CharField(max_length=20, default='', blank=True)
    description = models.TextField()

    def __str__(self):
        return self.name

   
    
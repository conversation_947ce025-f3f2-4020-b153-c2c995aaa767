from django.contrib import admin

# Register your models here.
from authz.models.users import User, UserDetails, UserFacilityMapping
from authz.models.facility import Facility, FacilityInventory
from authz.models.roles import CompanyGroup
from authz.models.company import CompanyMaster

from django.contrib.auth.admin import UserAdmin as BaseUserAdmin


@admin.register(User)
class UserAdmin(admin.ModelAdmin):
    list_display = ('username', 'email', 'role', 'is_staff', 'is_active')
    list_filter = ('role', 'is_active')
    search_fields = ('username', 'email')

admin.site.register(UserDetails)
admin.site.register(CompanyGroup)
@admin.register(Facility)
class FacilityAdmin(admin.ModelAdmin):
    list_display = ('name', 'facility_type', 'address', 'city', 'state', 'get_assigned_clusters')
    search_fields = ('name', 'address', 'city', 'state')
    list_filter = ('facility_type', 'city')

    def get_assigned_clusters(self, obj):
        # This will return a comma-separated string of cluster names assigned to the facility
        return ", ".join([cluster.name for cluster in obj.clusters.all()])
    get_assigned_clusters.short_description = "Clusters"


@admin.register(FacilityInventory)
class FacilityInventoryAdmin(admin.ModelAdmin):
    list_display = ('facility', 'product_variant', 'stock', 'price', 'is_active')
    search_fields = ('facility__name', 'product_variant__name')  # You can search by the manager's username

    # Include manager field in the admin form
    fieldsets = (
        (None, {
            'fields': ('facility', 'product_variant', 'stock', 'price', 'tax','csp','cust_discount','max_purchase_limit','outofstock_threshold','status','is_active')
        }),
    )
admin.site.register(CompanyMaster)
admin.site.register(UserFacilityMapping)

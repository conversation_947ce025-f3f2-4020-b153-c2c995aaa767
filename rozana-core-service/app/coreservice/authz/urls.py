from django.urls import path, include
from rest_framework_simplejwt.views import TokenRefreshView
from .views import LoginAPIView, UserProfileView, UserViewSet
from products.views import MediaFileUploadView
from rest_framework.routers import DefaultRouter

router = DefaultRouter()
router.register(r'users', UserViewSet, basename='user')

from rest_framework_simplejwt.views import (
    TokenObtainPairView,
    TokenRefreshView,
)

urlpatterns = [
    path('', include(router.urls)),
    path('login/', LoginAPIView.as_view(), name='login'),
    path('profile/', UserProfileView.as_view(), name='user_profile'),
    path('details/', UserViewSet.as_view({'get': 'details'}), name='user-details'),
    path('token/', TokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('upload/', MediaFileUploadView.as_view(), name='upload-files'),
]

from products.utils.pagination import CustomPageNumberPagination
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework import viewsets
from .models import User, Facility
from authz.models.facility import Cluster, FacilityInventory
from rest_framework.decorators import action
from rest_framework.filters import SearchFilter

from authz.serializers.users import LoginSerializer, UserSerializer
from authz.serializers.facility import FacilitySerializer, ClusterSerializer, ClusterListSerializer, FacilityInventorySerializer


class LoginAPIView(APIView):
    """
    API view for user login
    Returns JWT tokens upon successful authentication
    """
    permission_classes = [AllowAny]
    
    def post(self, request):
        serializer = LoginSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.validated_data['user']
            
            # Generate tokens
            refresh = RefreshToken.for_user(user)
            
            # Prepare response data
            response_data = {
                'refresh': str(refresh),
                'access': str(refresh.access_token),
                'user': UserSerializer(user).data
            }
            
            return Response(response_data, status=status.HTTP_200_OK)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class UserProfileView(APIView):
    """
    API view to get the current user's profile
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        serializer = UserSerializer(request.user)
        return Response(serializer.data, status=status.HTTP_200_OK)
    

class UserViewSet(viewsets.ModelViewSet):
    serializer_class = UserSerializer
    permission_classes = [IsAuthenticated]  # Only authenticated users can access this API

    def get_queryset(self):
        if self.action == 'retrieve':
            return User.objects.filter(id=self.kwargs['pk'])

        elif self.action == 'list':
            return User.objects.filter(role='manager')
        return User.objects.all()  # For other actions like update, delete, etc.

    def perform_create(self, serializer):
        user = serializer.save()
        password = self.request.data.get('password')
        if password:
            user.set_password(password)
            user.save()

    def perform_update(self, serializer):
        user = serializer.save()
        password = self.request.data.get('password')
        if password:
            user.set_password(password)
            user.save()

    @action(detail=False, methods=['get'], permission_classes=[IsAuthenticated])
    def details(self, request):
        """
        Custom action to get the details of the currently authenticated user.
        Accessible at /api/users/details/ with the token in the Authorization header.
        """
        user = request.user
        serializer = UserSerializer(user)
        return Response(serializer.data)


class FacilityViewSet(viewsets.ModelViewSet):
    queryset = Facility.objects.all()
    serializer_class = FacilitySerializer
    permission_classes = [IsAuthenticated]
    pagination_class = CustomPageNumberPagination
    filter_backends = (SearchFilter,)
    search_fields = ['name', 'facility_type']

    def perform_create(self, serializer):
        serializer.save()


class ClusterViewSet(viewsets.ModelViewSet):
    queryset = Cluster.objects.all()
    # serializer_class = ClusterSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = CustomPageNumberPagination
    filter_backends = (SearchFilter,)
    search_fields = ['name']

    def get_serializer_class(self):
        if self.action == 'list' or self.action == 'retrieve':
            return ClusterListSerializer
        else:
            return ClusterSerializer
            

class FacilityInventoryViewSet(viewsets.ModelViewSet):
    queryset = FacilityInventory.objects.all()
    serializer_class = FacilityInventorySerializer
    permission_classes = [IsAuthenticated]
    pagination_class = CustomPageNumberPagination
    filter_backends = (SearchFilter,)
    search_fields = ['facility', 'product_variant']

    def get_queryset(self):
        """
        Optionally restricts the returned facility inventories to a given facility.
        """
        queryset = super().get_queryset()
        
        # Check if 'facility_id' is passed as a query parameter
        facility_id = self.request.query_params.get('facility_id', None)
        
        if facility_id:
            queryset = queryset.filter(facility_id=facility_id)  # Filter by facility_id

        return queryset

    # def perform_create(self, serializer):
    #     serializer.save()

    @action(detail=False, methods=['post'], url_path='add')
    def bulk_create(self, request, *args, **kwargs):
        """
        Handles the bulk creation of FacilityInventory entries for a given facility.
        Expects a list of product_variant IDs and creates them in bulk.
        Avoids creating duplicates if the combination of facility_id and product_variant_id exists.
        """
        facility_id = request.data.get('facility')
        product_variant_ids = request.data.get('product_variant', [])

        if not facility_id:
            return Response({'error': 'Facility ID is required'}, status=400)

        if not product_variant_ids:
            return Response({'error': 'Product variant IDs are required'}, status=400)

        # Fetch the facility object (optional: validate existence)
        try:
            facility = Facility.objects.get(id=facility_id)
        except Facility.DoesNotExist:
            return Response({'error': 'Facility not found'}, status=400)

        # List to hold FacilityInventory objects to be created
        facility_inventories = []
        existing_combinations = FacilityInventory.objects.filter(
            facility_id=facility_id, 
            product_variant_id__in=product_variant_ids
        ).values_list('product_variant_id', flat=True)

        for product_variant_id in product_variant_ids:
            if product_variant_id in existing_combinations:
                # Skip if this combination already exists
                continue

            try:
                product_variant = ProductVariant.objects.get(id=product_variant_id)
            except ProductVariant.DoesNotExist:
                return Response({'error': f'Product variant with ID {product_variant_id} not found'}, status=400)

            # Create FacilityInventory instance
            facility_inventory = FacilityInventory(
                facility=facility,
                product_variant=product_variant,
                stock=0,  # Default to 0, can be updated later
                price=0.0,  # Default price, can be updated later
                tax=None,  # Default tax, can be added if needed
                csp=0.0,  # Default CSP, can be updated
                cust_discount=None,  # Optional customer discount
                max_purchase_limit=None,  # Optional
                outofstock_threshold=None,  # Optional
                status='Active',  # Default status
                is_active=True  # Default to active
            )

            facility_inventories.append(facility_inventory)

        # Bulk create all FacilityInventory entries that don't already exist
        if facility_inventories:
            FacilityInventory.objects.bulk_create(facility_inventories)

        return Response({"message": "Facility inventories created successfully."}, status=201)

# Generated by Django 5.2.3 on 2025-06-21 10:57

import coreservice.storage_backends
import django.contrib.auth.models
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
        ('authz', '0002_facility_address_facility_city_facility_country_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='user',
            name='roles',
        ),
        migrations.AlterField(
            model_name='companymaster',
            name='address',
            field=models.CharField(blank=True, default='', max_length=255),
        ),
        migrations.AlterField(
            model_name='companymaster',
            name='company_logo',
            field=models.ImageField(default='', null=True, storage=coreservice.storage_backends.AWSPrivateMediaStorage(), upload_to='images/companies/'),
        ),
        migrations.AlterField(
            model_name='facility',
            name='address',
            field=models.Char<PERSON>ield(default=1, max_length=100),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='facility',
            name='city',
            field=models.CharField(default=1, max_length=100),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='facility',
            name='company',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='facilities_company', to='authz.companymaster'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='facility',
            name='country',
            field=models.CharField(default=1, max_length=100),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='facility',
            name='latitude',
            field=models.CharField(default=1, max_length=100),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='facility',
            name='longitude',
            field=models.CharField(default=1, max_length=100),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='facility',
            name='pincode',
            field=models.CharField(default=1, max_length=100),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='facility',
            name='state',
            field=models.CharField(default=1, max_length=100),
            preserve_default=False,
        ),
        migrations.CreateModel(
            name='CompanyGroup',
            fields=[
                ('group_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='auth.group')),
                ('description', models.TextField(blank=True)),
                ('code_name', models.CharField(blank=True, max_length=150)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='company_groups', to='authz.companymaster')),
            ],
            options={
                'verbose_name': 'company group',
                'verbose_name_plural': 'company groups',
            },
            bases=('auth.group',),
            managers=[
                ('objects', django.contrib.auth.models.GroupManager()),
            ],
        ),
        migrations.AlterField(
            model_name='user',
            name='groups',
            field=models.ManyToManyField(blank=True, related_name='users', to='authz.companygroup', verbose_name='groups'),
        ),
        migrations.DeleteModel(
            name='Role',
        ),
    ]

# Generated by Django 5.2.3 on 2025-06-21 04:02

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('authz', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='facility',
            name='address',
            field=models.Char<PERSON>ield(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='facility',
            name='city',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='facility',
            name='country',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='facility',
            name='latitude',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='facility',
            name='longitude',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name='facility',
            name='pincode',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='facility',
            name='state',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.DeleteModel(
            name='FacilityDetails',
        ),
    ]
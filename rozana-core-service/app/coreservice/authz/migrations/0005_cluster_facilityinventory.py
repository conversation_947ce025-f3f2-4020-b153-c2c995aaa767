# Generated by Django 5.2.4 on 2025-08-05 12:10

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('authz', '0004_remove_user_email_verified_remove_user_user_type_and_more'),
        ('products', '0007_language_tax_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Cluster',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255)),
                ('latitude', models.TextField(blank=True, null=True)),
                ('longitude', models.TextField(blank=True, null=True)),
                ('region', models.CharField(blank=True, max_length=255, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('facilities', models.ManyToManyField(blank=True, related_name='clusters', to='authz.facility')),
            ],
            options={
                'db_table': 'clusters',
                'unique_together': {('name',)},
            },
        ),
        migrations.CreateModel(
            name='FacilityInventory',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('stock', models.IntegerField(default=0)),
                ('price', models.FloatField(default=0.0)),
                ('csp', models.FloatField(default=0.0)),
                ('cust_discount', models.IntegerField(blank=True, null=True)),
                ('max_purchase_limit', models.IntegerField(blank=True, null=True)),
                ('outofstock_threshold', models.IntegerField(blank=True, null=True)),
                ('status', models.CharField(blank=True, max_length=100, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('facility', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='facility_inventories', to='authz.facility')),
                ('product_variant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='facility_inventories', to='products.productvariant')),
                ('tax', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='products.tax')),
            ],
            options={
                'db_table': 'facility_inventories',
                'unique_together': {('facility', 'product_variant')},
            },
        ),
    ]

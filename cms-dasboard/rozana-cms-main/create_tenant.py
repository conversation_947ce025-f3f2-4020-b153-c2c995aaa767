from customers.models import Client, Domain
from datetime import date

# Create a new tenant
client = Client(
    schema_name='tenant2',
    name='Tenant 2',
    paid_until=date(2030, 1, 1),
    on_trial=True
)
client.save()  # This will also create the schema

# docker compose run --rm web python manage.py shell < create_tenant.py

# Create a domain for the tenant
Domain.objects.create(
    domain='tenant2.localhost',  # Change to your domain
    tenant=client,
    is_primary=True
)

print('Tenant and domain created successfully!')

# docker compose run --rm web python manage.py shell --command="exec(open('create_tenant.py').read())"
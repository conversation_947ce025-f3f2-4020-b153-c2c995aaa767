{"info": {"name": "Rozana CMS API Collection", "description": "Complete API collection for Rozana CMS Django project with all endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_postman_id": "rozana-cms-api-collection", "version": {"major": 1, "minor": 0, "patch": 0}}, "item": [{"name": "Authentication", "description": "User authentication endpoints", "item": [{"name": "Login - Get JWT Token", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"admin\",\n  \"password\": \"admin123\"\n}"}, "url": {"raw": "{{base_url}}/api/user/token/", "host": ["{{base_url}}"], "path": ["api", "user", "token", ""]}, "description": "Authenticate user and get JWT access and refresh tokens"}, "response": []}, {"name": "Refresh JWT Token", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refresh\": \"{{refresh_token}}\"\n}"}, "url": {"raw": "{{base_url}}/api/user/token/refresh/", "host": ["{{base_url}}"], "path": ["api", "user", "token", "refresh", ""]}, "description": "Refresh JWT access token using refresh token"}, "response": []}]}, {"name": "User Management", "description": "User CRUD operations", "item": [{"name": "Get User Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/user/details/", "host": ["{{base_url}}"], "path": ["api", "user", "details", ""]}, "description": "Get current authenticated user details"}, "response": []}, {"name": "List Users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/user/users/", "host": ["{{base_url}}"], "path": ["api", "user", "users", ""]}, "description": "List all users (managers only)"}, "response": []}, {"name": "Create User", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"testuser\",\n  \"first_name\": \"Test\",\n  \"last_name\": \"User\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"testpass123\",\n  \"role\": \"manager\",\n  \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/api/user/users/", "host": ["{{base_url}}"], "path": ["api", "user", "users", ""]}, "description": "Create a new user"}, "response": []}, {"name": "Get User by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/user/users/{{user_id}}/", "host": ["{{base_url}}"], "path": ["api", "user", "users", "{{user_id}}", ""]}, "description": "Get specific user by ID"}, "response": []}, {"name": "Update User", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"updateduser\",\n  \"first_name\": \"Updated\",\n  \"last_name\": \"User\",\n  \"email\": \"<EMAIL>\",\n  \"role\": \"manager\",\n  \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/api/user/users/{{user_id}}/", "host": ["{{base_url}}"], "path": ["api", "user", "users", "{{user_id}}", ""]}, "description": "Update user information"}, "response": []}, {"name": "Delete User", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/user/users/{{user_id}}/", "host": ["{{base_url}}"], "path": ["api", "user", "users", "{{user_id}}", ""]}, "description": "Delete a user"}, "response": []}]}, {"name": "Categories", "description": "Category management endpoints", "item": [{"name": "List Categories", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/cms/categories/", "host": ["{{base_url}}"], "path": ["api", "cms", "categories", ""]}, "description": "Get all categories with pagination and search"}, "response": []}, {"name": "Create Category", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Electronics\",\n  \"description\": \"Electronic items and gadgets\",\n  \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/api/cms/categories/", "host": ["{{base_url}}"], "path": ["api", "cms", "categories", ""]}, "description": "Create a new category"}, "response": []}, {"name": "Get Category by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/cms/categories/{{category_id}}/", "host": ["{{base_url}}"], "path": ["api", "cms", "categories", "{{category_id}}", ""]}, "description": "Get specific category by ID"}, "response": []}, {"name": "Update Category", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Electronics\",\n  \"description\": \"Updated electronic items and gadgets\",\n  \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/api/cms/categories/{{category_id}}/", "host": ["{{base_url}}"], "path": ["api", "cms", "categories", "{{category_id}}", ""]}, "description": "Update category information"}, "response": []}, {"name": "Delete Category", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/cms/categories/{{category_id}}/", "host": ["{{base_url}}"], "path": ["api", "cms", "categories", "{{category_id}}", ""]}, "description": "Delete a category"}, "response": []}, {"name": "Search Categories", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/cms/categories/?search=electronics", "host": ["{{base_url}}"], "path": ["api", "cms", "categories", ""], "query": [{"key": "search", "value": "electronics"}]}, "description": "Search categories by name"}, "response": []}]}, {"name": "Subcategories", "description": "Subcategory management endpoints", "item": [{"name": "List Subcategories", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/cms/subcategories/", "host": ["{{base_url}}"], "path": ["api", "cms", "subcategories", ""]}, "description": "Get all subcategories"}, "response": []}, {"name": "Create Subcategory", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Smartphones\",\n  \"description\": \"Mobile phones and smartphones\",\n  \"category\": {{category_id}},\n  \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/api/cms/subcategories/", "host": ["{{base_url}}"], "path": ["api", "cms", "subcategories", ""]}, "description": "Create a new subcategory"}, "response": []}, {"name": "Get Subcategory by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/cms/subcategories/{{subcategory_id}}/", "host": ["{{base_url}}"], "path": ["api", "cms", "subcategories", "{{subcategory_id}}", ""]}, "description": "Get specific subcategory by ID"}, "response": []}, {"name": "Update Subcategory", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Smartphones\",\n  \"description\": \"Updated mobile phones and smartphones\",\n  \"category\": {{category_id}},\n  \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/api/cms/subcategories/{{subcategory_id}}/", "host": ["{{base_url}}"], "path": ["api", "cms", "subcategories", "{{subcategory_id}}", ""]}, "description": "Update subcategory information"}, "response": []}, {"name": "Delete Subcategory", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/cms/subcategories/{{subcategory_id}}/", "host": ["{{base_url}}"], "path": ["api", "cms", "subcategories", "{{subcategory_id}}", ""]}, "description": "Delete a subcategory"}, "response": []}]}, {"name": "Sub-subcategories", "description": "Sub-subcategory management endpoints", "item": [{"name": "List Sub-subcategories", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/cms/subsubcategories/", "host": ["{{base_url}}"], "path": ["api", "cms", "subsubcategories", ""]}, "description": "Get all sub-subcategories"}, "response": []}, {"name": "Create Sub-subcategory", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Android Phones\",\n  \"description\": \"Android smartphones\",\n  \"category\": {{category_id}},\n  \"subcategory\": {{subcategory_id}},\n  \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/api/cms/subsubcategories/", "host": ["{{base_url}}"], "path": ["api", "cms", "subsubcategories", ""]}, "description": "Create a new sub-subcategory"}, "response": []}]}, {"name": "Brands", "description": "Brand management endpoints", "item": [{"name": "List Brands", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/cms/brands/", "host": ["{{base_url}}"], "path": ["api", "cms", "brands", ""]}, "description": "Get all brands"}, "response": []}, {"name": "Create Brand", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Samsung\",\n  \"description\": \"Samsung Electronics\",\n  \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/api/cms/brands/", "host": ["{{base_url}}"], "path": ["api", "cms", "brands", ""]}, "description": "Create a new brand"}, "response": []}, {"name": "Get Brand by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/cms/brands/{{brand_id}}/", "host": ["{{base_url}}"], "path": ["api", "cms", "brands", "{{brand_id}}", ""]}, "description": "Get specific brand by ID"}, "response": []}, {"name": "Update Brand", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Samsung Electronics\",\n  \"description\": \"Updated Samsung Electronics description\",\n  \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/api/cms/brands/{{brand_id}}/", "host": ["{{base_url}}"], "path": ["api", "cms", "brands", "{{brand_id}}", ""]}, "description": "Update brand information"}, "response": []}, {"name": "Delete Brand", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/cms/brands/{{brand_id}}/", "host": ["{{base_url}}"], "path": ["api", "cms", "brands", "{{brand_id}}", ""]}, "description": "Delete a brand"}, "response": []}]}, {"name": "Facilities & Clusters", "description": "Facility and cluster management endpoints", "item": [{"name": "List Clusters", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/cms/clusters/", "host": ["{{base_url}}"], "path": ["api", "cms", "clusters", ""]}, "description": "Get all clusters"}, "response": []}, {"name": "Create Cluster", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"North Cluster\",\n  \"description\": \"North region cluster\",\n  \"latitude\": 12.9716,\n  \"longitude\": 77.5946,\n  \"address\": \"Bangalore North\",\n  \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/api/cms/clusters/", "host": ["{{base_url}}"], "path": ["api", "cms", "clusters", ""]}, "description": "Create a new cluster"}, "response": []}, {"name": "List Facilities", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/cms/facilities/", "host": ["{{base_url}}"], "path": ["api", "cms", "facilities", ""]}, "description": "Get all facilities"}, "response": []}, {"name": "Create Facility", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Main Warehouse\",\n  \"facility_type\": \"warehouse\",\n  \"address\": \"123 Main Street, Bangalore\",\n  \"phone\": \"+91-9876543210\",\n  \"email\": \"<EMAIL>\",\n  \"manager\": {{user_id}},\n  \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/api/cms/facilities/", "host": ["{{base_url}}"], "path": ["api", "cms", "facilities", ""]}, "description": "Create a new facility"}, "response": []}, {"name": "Get Facility by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/cms/facilities/{{facility_id}}/", "host": ["{{base_url}}"], "path": ["api", "cms", "facilities", "{{facility_id}}", ""]}, "description": "Get specific facility by ID"}, "response": []}]}, {"name": "Products", "description": "Product management endpoints", "item": [{"name": "List Products", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/cms/products/", "host": ["{{base_url}}"], "path": ["api", "cms", "products", ""]}, "description": "Get all products with pagination"}, "response": []}, {"name": "Create Product", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Samsung Galaxy S21\",\n  \"description\": \"Latest Samsung smartphone\",\n  \"slug\": \"samsung-galaxy-s21\",\n  \"category\": {{category_id}},\n  \"subcategory\": {{subcategory_id}},\n  \"subsubcategory\": {{subsubcategory_id}},\n  \"brand\": {{brand_id}},\n  \"tags\": \"smartphone,android,samsung\",\n  \"is_active\": true,\n  \"product_images\": [\n    {\n      \"image_url\": \"https://example.com/galaxy-s21-1.jpg\",\n      \"alt_text\": \"Galaxy S21 Front View\",\n      \"is_primary\": true\n    }\n  ],\n  \"variants\": [\n    {\n      \"name\": \"Galaxy S21 128GB\",\n      \"sku\": \"SAM-GS21-128\",\n      \"weight\": 171.0,\n      \"dimensions\": \"151.7 x 71.2 x 7.9 mm\",\n      \"color\": \"Phantom Gray\",\n      \"size\": \"128GB\",\n      \"material\": \"Glass\",\n      \"net_qty\": 1,\n      \"is_active\": true\n    }\n  ],\n  \"facility_ids\": [],\n  \"collections\": [],\n  \"linked_variants\": []\n}"}, "url": {"raw": "{{base_url}}/api/cms/products/", "host": ["{{base_url}}"], "path": ["api", "cms", "products", ""]}, "description": "Create a new product with variants and images"}, "response": []}, {"name": "Get Product by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/cms/products/{{product_id}}/", "host": ["{{base_url}}"], "path": ["api", "cms", "products", "{{product_id}}", ""]}, "description": "Get specific product by ID with full details"}, "response": []}, {"name": "Update Product", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Samsung Galaxy S21 Updated\",\n  \"description\": \"Updated Samsung smartphone description\",\n  \"slug\": \"samsung-galaxy-s21-updated\",\n  \"category\": {{category_id}},\n  \"subcategory\": {{subcategory_id}},\n  \"subsubcategory\": {{subsubcategory_id}},\n  \"brand\": {{brand_id}},\n  \"tags\": \"smartphone,android,samsung,updated\",\n  \"is_active\": true,\n  \"product_images\": [],\n  \"variants\": [],\n  \"facility_ids\": [],\n  \"collections\": [],\n  \"linked_variants\": []\n}"}, "url": {"raw": "{{base_url}}/api/cms/products/{{product_id}}/", "host": ["{{base_url}}"], "path": ["api", "cms", "products", "{{product_id}}", ""]}, "description": "Update product information"}, "response": []}, {"name": "Delete Product", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/cms/products/{{product_id}}/", "host": ["{{base_url}}"], "path": ["api", "cms", "products", "{{product_id}}", ""]}, "description": "Delete a product"}, "response": []}, {"name": "Update Product Status", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"is_active\": false\n}"}, "url": {"raw": "{{base_url}}/api/cms/products/{{product_id}}/status/", "host": ["{{base_url}}"], "path": ["api", "cms", "products", "{{product_id}}", "status", ""]}, "description": "Update product active status"}, "response": []}, {"name": "Search Products", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/cms/products/?search=samsung", "host": ["{{base_url}}"], "path": ["api", "cms", "products", ""], "query": [{"key": "search", "value": "samsung"}]}, "description": "Search products by name, description, slug, or tags"}, "response": []}, {"name": "Product List (Alternative Endpoint)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/cms/product/list/", "host": ["{{base_url}}"], "path": ["api", "cms", "product", "list", ""]}, "description": "Alternative product listing endpoint"}, "response": []}]}, {"name": "Product Variants", "description": "Product variant management endpoints", "item": [{"name": "List Product Variants", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/cms/variants/", "host": ["{{base_url}}"], "path": ["api", "cms", "variants", ""]}, "description": "Get all product variants"}, "response": []}, {"name": "Create Product Variant", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Galaxy S21 256GB\",\n  \"product\": {{product_id}},\n  \"sku\": \"SAM-GS21-256\",\n  \"ean_number\": \"1234567890123\",\n  \"weight\": 171.0,\n  \"dimensions\": \"151.7 x 71.2 x 7.9 mm\",\n  \"color\": \"Phantom White\",\n  \"size\": \"256GB\",\n  \"material\": \"Glass\",\n  \"net_qty\": 1,\n  \"csp\": 75000.00,\n  \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/api/cms/variants/", "host": ["{{base_url}}"], "path": ["api", "cms", "variants", ""]}, "description": "Create a new product variant"}, "response": []}, {"name": "Get Product Variant by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/cms/variants/{{variant_id}}/", "host": ["{{base_url}}"], "path": ["api", "cms", "variants", "{{variant_id}}", ""]}, "description": "Get specific product variant by ID"}, "response": []}, {"name": "Update Product Variant", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Galaxy S21 256GB Updated\",\n  \"product\": {{product_id}},\n  \"sku\": \"SAM-GS21-256-UPD\",\n  \"ean_number\": \"1234567890123\",\n  \"weight\": 171.0,\n  \"dimensions\": \"151.7 x 71.2 x 7.9 mm\",\n  \"color\": \"Phantom White\",\n  \"size\": \"256GB\",\n  \"material\": \"Glass\",\n  \"net_qty\": 1,\n  \"csp\": 78000.00,\n  \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/api/cms/variants/{{variant_id}}/", "host": ["{{base_url}}"], "path": ["api", "cms", "variants", "{{variant_id}}", ""]}, "description": "Update product variant information"}, "response": []}, {"name": "Delete Product Variant", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/cms/variants/{{variant_id}}/", "host": ["{{base_url}}"], "path": ["api", "cms", "variants", "{{variant_id}}", ""]}, "description": "Delete a product variant"}, "response": []}]}, {"name": "Collections", "description": "Product collection management endpoints", "item": [{"name": "List Collections", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/cms/collections/", "host": ["{{base_url}}"], "path": ["api", "cms", "collections", ""]}, "description": "Get all product collections"}, "response": []}, {"name": "Create Collection", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Summer Collection\",\n  \"description\": \"Summer special products\",\n  \"is_active\": true,\n  \"products\": []\n}"}, "url": {"raw": "{{base_url}}/api/cms/collections/", "host": ["{{base_url}}"], "path": ["api", "cms", "collections", ""]}, "description": "Create a new product collection"}, "response": []}, {"name": "Get Collection by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/cms/collections/{{collection_id}}/", "host": ["{{base_url}}"], "path": ["api", "cms", "collections", "{{collection_id}}", ""]}, "description": "Get specific collection by ID"}, "response": []}, {"name": "Update Collection", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Summer Collection Updated\",\n  \"description\": \"Updated summer special products\",\n  \"is_active\": true,\n  \"products\": [{{product_id}}]\n}"}, "url": {"raw": "{{base_url}}/api/cms/collections/{{collection_id}}/", "host": ["{{base_url}}"], "path": ["api", "cms", "collections", "{{collection_id}}", ""]}, "description": "Update collection information"}, "response": []}, {"name": "Delete Collection", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/cms/collections/{{collection_id}}/", "host": ["{{base_url}}"], "path": ["api", "cms", "collections", "{{collection_id}}", ""]}, "description": "Delete a collection"}, "response": []}]}, {"name": "Facility Inventory", "description": "Facility inventory management endpoints", "item": [{"name": "List Facility Inventory", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/cms/facilityinventory/", "host": ["{{base_url}}"], "path": ["api", "cms", "facilityinventory", ""]}, "description": "Get all facility inventory items"}, "response": []}, {"name": "List Facility Inventory by Facility", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/cms/facilityinventory/?facility_id={{facility_id}}", "host": ["{{base_url}}"], "path": ["api", "cms", "facilityinventory", ""], "query": [{"key": "facility_id", "value": "{{facility_id}}"}]}, "description": "Get facility inventory filtered by facility ID"}, "response": []}, {"name": "Create Facility Inventory", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"facility\": {{facility_id}},\n  \"product_variant\": {{variant_id}},\n  \"stock\": 100,\n  \"price\": 75000.00,\n  \"tax\": 18.0,\n  \"csp\": 75000.00,\n  \"cust_discount\": 5.0,\n  \"max_purchase_limit\": 5,\n  \"outofstock_threshold\": 10,\n  \"status\": \"Active\",\n  \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/api/cms/facilityinventory/", "host": ["{{base_url}}"], "path": ["api", "cms", "facilityinventory", ""]}, "description": "Create facility inventory item"}, "response": []}, {"name": "Bulk Add Facility Inventory", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"facility\": {{facility_id}},\n  \"product_variant\": [{{variant_id}}, {{variant_id_2}}]\n}"}, "url": {"raw": "{{base_url}}/api/cms/facilityinventory/add/", "host": ["{{base_url}}"], "path": ["api", "cms", "facilityinventory", "add", ""]}, "description": "Bulk create facility inventory items for multiple variants"}, "response": []}, {"name": "Get Facility Inventory by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/cms/facilityinventory/{{inventory_id}}/", "host": ["{{base_url}}"], "path": ["api", "cms", "facilityinventory", "{{inventory_id}}", ""]}, "description": "Get specific facility inventory by ID"}, "response": []}, {"name": "Update Facility Inventory", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"facility\": {{facility_id}},\n  \"product_variant\": {{variant_id}},\n  \"stock\": 150,\n  \"price\": 78000.00,\n  \"tax\": 18.0,\n  \"csp\": 78000.00,\n  \"cust_discount\": 7.0,\n  \"max_purchase_limit\": 3,\n  \"outofstock_threshold\": 15,\n  \"status\": \"Active\",\n  \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/api/cms/facilityinventory/{{inventory_id}}/", "host": ["{{base_url}}"], "path": ["api", "cms", "facilityinventory", "{{inventory_id}}", ""]}, "description": "Update facility inventory information"}, "response": []}, {"name": "Delete Facility Inventory", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/cms/facilityinventory/{{inventory_id}}/", "host": ["{{base_url}}"], "path": ["api", "cms", "facilityinventory", "{{inventory_id}}", ""]}, "description": "Delete facility inventory item"}, "response": []}]}, {"name": "File Upload", "description": "Media file upload endpoints", "item": [{"name": "Upload Files", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "files", "type": "file", "src": [], "description": "Select one or more files to upload"}]}, "url": {"raw": "{{base_url}}/api/cms/upload/", "host": ["{{base_url}}"], "path": ["api", "cms", "upload", ""]}, "description": "Upload media files (images, documents, etc.)"}, "response": []}]}], "variable": [{"key": "base_url", "value": "http://localhost:8000", "description": "Base URL for the API"}, {"key": "access_token", "value": "", "description": "JWT access token for authentication"}, {"key": "refresh_token", "value": "", "description": "JWT refresh token"}, {"key": "user_id", "value": "1", "description": "User ID for testing"}, {"key": "category_id", "value": "1", "description": "Category ID for testing"}, {"key": "subcategory_id", "value": "1", "description": "Subcategory ID for testing"}, {"key": "subsubcategory_id", "value": "1", "description": "Sub-subcategory ID for testing"}, {"key": "brand_id", "value": "1", "description": "Brand ID for testing"}, {"key": "product_id", "value": "1", "description": "Product ID for testing"}, {"key": "variant_id", "value": "1", "description": "Product variant ID for testing"}, {"key": "variant_id_2", "value": "2", "description": "Second product variant ID for testing"}, {"key": "collection_id", "value": "1", "description": "Collection ID for testing"}, {"key": "facility_id", "value": "1", "description": "Facility ID for testing"}, {"key": "cluster_id", "value": "1", "description": "Cluster ID for testing"}, {"key": "inventory_id", "value": "1", "description": "Facility inventory ID for testing"}]}
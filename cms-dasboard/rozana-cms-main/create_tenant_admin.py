from customers.models import Client
from django.contrib.auth import get_user_model
from django_tenants.utils import schema_context

# Settings for the new tenant admin
ADMIN_USERNAME = 'admin'
ADMIN_EMAIL = '<EMAIL>'
ADMIN_PASSWORD = 'adminpassword123'  # Change this in production!

# Get the tenant
client = Client.objects.get(schema_name='tenant2')

# Switch to tenant schema and create superuser
with schema_context(client.schema_name):
    User = get_user_model()
    if not User.objects.filter(username=ADMIN_USERNAME).exists():
        User.objects.create_superuser(
            username=ADMIN_USERNAME,
            email=ADMIN_EMAIL,
            password=ADMIN_PASSWORD
        )
        print('Admin user created for tenant2!')
    else:
        print('Admin user already exists for tenant2.')

# docker compose run --rm web python manage.py shell --command="exec(open('create_tenant_admin.py').read())"
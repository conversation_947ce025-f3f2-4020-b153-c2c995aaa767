from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticated
from .models import User
from .serializers import UserSerializer
from rest_framework.response import Response
from rest_framework.decorators import action

class UserViewSet(viewsets.ModelViewSet):
    serializer_class = UserSerializer
    permission_classes = [IsAuthenticated]  # Only authenticated users can access this API

    def get_queryset(self):
        if self.action == 'retrieve':
            return User.objects.filter(id=self.kwargs['pk'])

        elif self.action == 'list':
            return User.objects.filter(role='manager')
        return User.objects.all()  # For other actions like update, delete, etc.

    def perform_create(self, serializer):
        user = serializer.save()
        password = self.request.data.get('password')
        if password:
            user.set_password(password)
            user.save()

    def perform_update(self, serializer):
        user = serializer.save()
        password = self.request.data.get('password')
        if password:
            user.set_password(password)
            user.save()

    @action(detail=False, methods=['get'], permission_classes=[IsAuthenticated])
    def details(self, request):
        """
        Custom action to get the details of the currently authenticated user.
        Accessible at /api/users/details/ with the token in the Authorization header.
        """
        user = request.user
        serializer = UserSerializer(user)
        return Response(serializer.data)


from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>outer
from .views import UserViewSet

router = DefaultRouter()
router.register(r'users', UserViewSet, basename='user')

from rest_framework_simplejwt.views import (
    TokenObtainPairView,
    TokenRefreshView,
)

urlpatterns = [
    path('', include(router.urls)),
    path('details/', UserViewSet.as_view({'get': 'details'}), name='user-details'),  # Custom endpoint for user details
    path('token/', TokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
]



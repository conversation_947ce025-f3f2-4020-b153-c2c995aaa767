from django.contrib.auth.models import AbstractUser
from django.db import models

class User(AbstractUser):
    MASTER = 'master'
    MANAGER = 'manager'
    USER_ROLES = [
        (MASTER, 'Master'),
        (MANAGER, 'Manager'),
    ]

    role = models.CharField(
        max_length=20,
        choices=USER_ROLES,
        default=MANAGER,  # Default role is Manager
    )

    def __str__(self):
        return f"{self.first_name} {self.last_name} ({self.email})"
# Generated by Django 5.2.4 on 2025-07-23 09:37

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cms', '0014_alter_productvariant_ean_number'),
    ]

    operations = [
        migrations.CreateModel(
            name='Language',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
            ],
        ),
        migrations.AlterUniqueTogether(
            name='product',
            unique_together={('sku',)},
        ),
        migrations.AlterField(
            model_name='facility',
            name='servicable_area',
            field=models.TextField(blank=True, default=list, null=True),
        ),
        migrations.AlterField(
            model_name='product',
            name='brand',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='products', to='cms.brand'),
        ),
        migrations.RemoveField(
            model_name='product',
            name='is_visible',
        ),
        migrations.RemoveField(
            model_name='product',
            name='name_hi',
        ),
        migrations.RemoveField(
            model_name='product',
            name='name_kn',
        ),
        migrations.RemoveField(
            model_name='product',
            name='tags_hi',
        ),
        migrations.RemoveField(
            model_name='product',
            name='tags_kn',
        ),
        migrations.CreateModel(
            name='ProductDetail',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(blank=True, max_length=255, null=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('tags', models.JSONField(blank=True, default=list, null=True)),
                ('language', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='cms.language')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='product_details', to='cms.product')),
            ],
            options={
                'unique_together': {('product', 'language')},
            },
        ),
    ]

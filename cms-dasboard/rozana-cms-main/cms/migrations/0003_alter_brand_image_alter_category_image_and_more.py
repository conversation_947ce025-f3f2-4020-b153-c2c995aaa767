# Generated by Django 5.2.4 on 2025-07-08 19:10

import cms.models.models
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cms', '0002_alter_facility_cluster'),
    ]

    operations = [
        migrations.AlterField(
            model_name='brand',
            name='image',
            field=models.ImageField(blank=True, help_text='Upload image file. Original stored with filename, WebP versions generated automatically.', null=True, storage=cms.models.models.ImageStorage(), upload_to='Brand/'),
        ),
        migrations.AlterField(
            model_name='category',
            name='image',
            field=models.ImageField(blank=True, help_text='Upload image file. Original stored with filename, WebP versions generated automatically.', null=True, storage=cms.models.models.ImageStorage(), upload_to='Category/'),
        ),
        migrations.AlterField(
            model_name='product',
            name='image',
            field=models.ImageField(blank=True, help_text='Upload an image file. Will be converted to WebP format and stored in S3.', null=True, storage=cms.models.models.ImageStorage(), upload_to='Product/'),
        ),
        migrations.AlterField(
            model_name='subcategory',
            name='image',
            field=models.ImageField(blank=True, help_text='Upload image file. Original stored with filename, WebP versions generated automatically.', null=True, storage=cms.models.models.ImageStorage(), upload_to='Subcategory/'),
        ),
        migrations.AlterField(
            model_name='subsubcategory',
            name='image',
            field=models.ImageField(blank=True, help_text='Upload image file. Original stored with filename, WebP versions generated automatically.', null=True, storage=cms.models.models.ImageStorage(), upload_to='Childsubcategory/'),
        ),
        migrations.CreateModel(
            name='ProductImage',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('priority', models.PositiveIntegerField(default=1)),
                ('image', models.ImageField(blank=True, help_text='Upload an image file. Will be converted to WebP format (500x500, 600x600, 800x800) and stored in S3.', null=True, storage=cms.models.models.ImageStorage(), upload_to='ProductImages/')),
                ('alt_text', models.CharField(blank=True, max_length=255, null=True)),
                ('is_primary', models.BooleanField(default=False)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='product_images', to='cms.product')),
            ],
            options={
                'verbose_name': 'Product Image',
                'verbose_name_plural': 'Product Images',
                'db_table': 'product_images',
                'ordering': ['priority', 'id'],
                'unique_together': {('product', 'priority')},
            },
        ),
    ]

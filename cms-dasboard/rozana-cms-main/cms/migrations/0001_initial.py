# Generated by Django 5.2.4 on 2025-07-08 13:23

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Brand',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('image', models.ImageField(blank=True, help_text='Upload image file. Original stored with filename, WebP versions generated automatically.', null=True, upload_to='Brand/')),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name_plural': 'Brands',
                'db_table': 'brands',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('image', models.ImageField(blank=True, help_text='Upload image file. Original stored with filename, WebP versions generated automatically.', null=True, upload_to='Category/')),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name_plural': 'Categories',
                'db_table': 'categories',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Cluster',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255)),
                ('latitude', models.TextField(blank=True, null=True)),
                ('longitude', models.TextField(blank=True, null=True)),
                ('region', models.CharField(blank=True, max_length=255, null=True)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'db_table': 'clusters',
                'unique_together': {('name',)},
            },
        ),
        migrations.CreateModel(
            name='Facility',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('facility_type', models.CharField(choices=[('store', 'Store'), ('warehouse', 'Warehouse')], max_length=100)),
                ('name', models.CharField(max_length=255)),
                ('address', models.CharField(max_length=255)),
                ('city', models.CharField(max_length=100)),
                ('state', models.CharField(max_length=100)),
                ('country', models.CharField(max_length=100)),
                ('pincode', models.CharField(max_length=100)),
                ('latitude', models.CharField(max_length=100)),
                ('longitude', models.CharField(max_length=100)),
                ('is_active', models.BooleanField(default=True)),
                ('cluster', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='facilities', to='cms.cluster')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
                ('manager', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_facilities', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'facilities',
                'unique_together': {('name',)},
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255)),
                ('name_hi', models.CharField(blank=True, max_length=255, null=True)),
                ('name_kn', models.CharField(blank=True, max_length=255, null=True)),
                ('slug', models.CharField(blank=True, max_length=255, null=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('tags', models.JSONField(blank=True, default=list, null=True)),
                ('tags_hi', models.JSONField(blank=True, default=list, null=True)),
                ('tags_kn', models.JSONField(blank=True, default=list, null=True)),
                ('top_product', models.BooleanField(default=False)),
                ('status', models.CharField(blank=True, max_length=100, null=True)),
                ('additional_details', models.TextField(blank=True, null=True)),
                ('is_visible', models.BooleanField(default=True)),
                ('is_published', models.BooleanField(default=True)),
                ('is_active', models.BooleanField(default=True)),
                ('image_url', models.CharField(max_length=512)),
                ('image', models.ImageField(blank=True, help_text='Upload an image file. Will be converted to WebP format and stored in S3.', null=True, upload_to='Product/')),
                ('tax', models.FloatField()),
                ('brand', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='products', to='cms.brand')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='products', to='cms.category')),
            ],
            options={
                'db_table': 'products',
            },
        ),
        migrations.CreateModel(
            name='ProductVariant',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255)),
                ('slug', models.CharField(blank=True, max_length=255, null=True)),
                ('sku', models.CharField(blank=True, max_length=255, null=True)),
                ('price', models.FloatField()),
                ('mrp', models.FloatField()),
                ('csp', models.FloatField()),
                ('cust_discount', models.IntegerField(blank=True, null=True)),
                ('stock_quantity', models.IntegerField(blank=True, null=True)),
                ('max_purchase_limit', models.IntegerField(blank=True, null=True)),
                ('outofstock_threshold', models.IntegerField(blank=True, null=True)),
                ('ean_number', models.IntegerField(blank=True, null=True)),
                ('hsn_code', models.TextField(blank=True, null=True)),
                ('is_focused', models.BooleanField(default=False)),
                ('is_offer', models.BooleanField(default=False)),
                ('is_freebie', models.BooleanField(default=False)),
                ('base_qty', models.IntegerField(blank=True, default=1, null=True)),
                ('size', models.CharField(blank=True, max_length=255, null=True)),
                ('color', models.CharField(blank=True, max_length=255, null=True)),
                ('weight', models.CharField(blank=True, max_length=255, null=True)),
                ('packaging_type', models.CharField(blank=True, max_length=255, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='variants', to='cms.product')),
            ],
            options={
                'db_table': 'variants',
                'unique_together': {('name', 'sku', 'slug')},
            },
        ),
        migrations.CreateModel(
            name='Subcategory',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('image', models.ImageField(blank=True, help_text='Upload image file. Original stored with filename, WebP versions generated automatically.', null=True, upload_to='Subcategory/')),
                ('is_active', models.BooleanField(default=True)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='subcategories', to='cms.category')),
            ],
            options={
                'verbose_name_plural': 'Subcategories',
                'db_table': 'subcategories',
                'ordering': ['category__name', 'name'],
            },
        ),
        migrations.AddField(
            model_name='product',
            name='subcategory',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='products', to='cms.subcategory'),
        ),
        migrations.CreateModel(
            name='Subsubcategory',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('image', models.ImageField(blank=True, help_text='Upload image file. Original stored with filename, WebP versions generated automatically.', null=True, upload_to='Childsubcategory/')),
                ('is_active', models.BooleanField(default=True)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='subsubcategories', to='cms.category')),
                ('subcategory', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='subsubcategories', to='cms.subcategory')),
            ],
            options={
                'verbose_name_plural': 'Subsubcategories',
                'db_table': 'subsubcategories',
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='product',
            name='subsubcategory',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='products', to='cms.subsubcategory'),
        ),
        migrations.CreateModel(
            name='FacilityInventory',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('stock', models.IntegerField(default=0)),
                ('price', models.FloatField()),
                ('is_active', models.BooleanField(default=True)),
                ('facility', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='facility_inventories', to='cms.facility')),
                ('product_variant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='facility_inventories', to='cms.productvariant')),
            ],
            options={
                'db_table': 'facility_inventories',
                'unique_together': {('facility', 'product_variant')},
            },
        ),
        migrations.AlterUniqueTogether(
            name='product',
            unique_together={('slug',)},
        ),
    ]

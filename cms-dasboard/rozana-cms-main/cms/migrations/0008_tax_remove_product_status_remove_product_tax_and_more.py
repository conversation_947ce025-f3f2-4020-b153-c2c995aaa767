# Generated by Django 5.2.4 on 2025-07-11 12:09

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cms', '0007_alter_productimage_unique_together_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Tax',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('percentage', models.DecimalField(decimal_places=2, max_digits=5)),
                ('cgst_percentage', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('sgst_percentage', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('igst_percentage', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('cess_percentage', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'Tax',
                'verbose_name_plural': 'Taxes',
                'db_table': 'taxes',
            },
        ),
        migrations.RemoveField(
            model_name='product',
            name='status',
        ),
        migrations.RemoveField(
            model_name='product',
            name='tax',
        ),
        migrations.RemoveField(
            model_name='productvariant',
            name='base_qty',
        ),
        migrations.RemoveField(
            model_name='productvariant',
            name='csp',
        ),
        migrations.RemoveField(
            model_name='productvariant',
            name='cust_discount',
        ),
        migrations.RemoveField(
            model_name='productvariant',
            name='is_focused',
        ),
        migrations.RemoveField(
            model_name='productvariant',
            name='is_freebie',
        ),
        migrations.RemoveField(
            model_name='productvariant',
            name='is_offer',
        ),
        migrations.RemoveField(
            model_name='productvariant',
            name='max_purchase_limit',
        ),
        migrations.RemoveField(
            model_name='productvariant',
            name='outofstock_threshold',
        ),
        migrations.RemoveField(
            model_name='productvariant',
            name='stock_quantity',
        ),
        migrations.AddField(
            model_name='facility',
            name='servicable_area',
            field=models.JSONField(blank=True, default=list, null=True),
        ),
        migrations.AddField(
            model_name='facilityinventory',
            name='csp',
            field=models.FloatField(default=0.0),
        ),
        migrations.AddField(
            model_name='facilityinventory',
            name='cust_discount',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='facilityinventory',
            name='max_purchase_limit',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='facilityinventory',
            name='outofstock_threshold',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='facilityinventory',
            name='status',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='product',
            name='sku',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='productvariant',
            name='image',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='productvariant',
            name='ssp',
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='facilityinventory',
            name='price',
            field=models.FloatField(default=0.0),
        ),
        migrations.AlterField(
            model_name='productvariant',
            name='mrp',
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='productvariant',
            name='price',
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='facilityinventory',
            name='tax',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='cms.tax'),
        ),
    ]

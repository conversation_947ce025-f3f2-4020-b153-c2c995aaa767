# Generated by Django 5.2.4 on 2025-07-16 13:24

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cms', '0008_tax_remove_product_status_remove_product_tax_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Collection',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('name', models.Char<PERSON>ield(max_length=255)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('products', models.ManyToManyField(blank=True, null=True, related_name='collections', to='cms.product')),
            ],
            options={
                'db_table': 'collections',
            },
        ),
    ]

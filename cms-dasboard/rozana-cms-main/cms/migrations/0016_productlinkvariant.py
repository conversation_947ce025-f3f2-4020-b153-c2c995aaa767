# Generated by Django 5.2.4 on 2025-07-30 18:02

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cms', '0015_language_alter_product_unique_together_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ProductLinkVariant',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('linked_variant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='linked_variants', to='cms.productvariant')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='linked_variants', to='cms.product')),
            ],
            options={
                'db_table': 'product_link_variants',
                'unique_together': {('product', 'linked_variant')},
            },
        ),
    ]

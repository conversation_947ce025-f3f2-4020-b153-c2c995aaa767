# Generated by Django 5.2.4 on 2025-07-16 13:42

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cms', '0009_collection'),
    ]

    operations = [
        migrations.AddField(
            model_name='cluster',
            name='facilities',
            field=models.ManyToManyField(blank=True, related_name='clusters_set', to='cms.facility'),
        ),
        migrations.AlterField(
            model_name='cluster',
            name='latitude',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='cluster',
            name='longitude',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.RemoveField(
            model_name='facility',
            name='cluster',
        ),
        migrations.AddField(
            model_name='facility',
            name='cluster',
            field=models.ManyToManyField(blank=True, related_name='facilities_set', to='cms.cluster'),
        ),
    ]

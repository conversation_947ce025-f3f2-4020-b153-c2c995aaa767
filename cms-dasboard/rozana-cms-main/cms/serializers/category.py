from rest_framework import serializers
from cms.models.category import Category, Subcategory, Subsubcategory, Brand


class SubcategorySubsubcategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Subsubcategory  # Assuming you have a Subsubcategory model
        fields = ['id', 'name', 'description']

class CategorySubcategorySerializer(serializers.ModelSerializer):
    subsubcategories = SubcategorySubsubcategorySerializer(many=True, read_only=True)  # Nested subsubcategories

    class Meta:
        model = Subcategory
        fields = ['id', 'name', 'description', 'subsubcategories']

class CategoryListSerializer(serializers.ModelSerializer):
    subcategories = CategorySubcategorySerializer(many=True, read_only=True)  # Nested subcategories
    class Meta:
        model = Category
        fields = ['id', 'name', 'description', 'image', 'is_active', 'subcategories']

class CategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Category
        fields = ['id', 'name', 'description', 'image', 'is_active']

class SubcategorySerializer(serializers.ModelSerializer):
    category_name = serializers.CharField(source='category.name', read_only=True)

    class Meta:
        model = Subcategory
        fields = ['id', 'name', 'description', 'category', 'category_name', 'image', 'is_active']

class SubsubcategorySerializer(serializers.ModelSerializer):
    category_name = serializers.CharField(source='category.name', read_only=True)
    subcategory_name = serializers.CharField(source='subcategory.name', read_only=True)
    class Meta:
        model = Subsubcategory
        fields = ['id', 'name', 'description', 'category', 'category_name','subcategory', 'subcategory_name', 'image', 'is_active']

class BrandSerializer(serializers.ModelSerializer):
    class Meta:
        model = Brand
        fields = ['id', 'name', 'description', 'image', 'is_active']

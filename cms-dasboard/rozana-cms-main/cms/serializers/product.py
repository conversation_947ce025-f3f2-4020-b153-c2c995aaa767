from rest_framework import serializers
from cms.models.product import Product, ProductVariant, Collection, ProductLinkVariant
from cms.models.product_image import ProductImage
from cms.models.category import Category, Subcategory, Subsubcategory, Brand


class CategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Category
        fields = ['id', 'name']

class SubcategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Subcategory
        fields = ['id', 'name']

class SubsubcategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Subsubcategory
        fields = ['id', 'name']

class BrandSerializer(serializers.ModelSerializer):
    class Meta:
        model = Brand
        fields = ['id', 'name']

class ProductImageViewSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductImage
        fields = ['id','image','priority','alt_text','is_primary']

class ProductVariantViewSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductVariant
        fields = [
            'id','name','slug','sku','price','mrp',
            'ean_number','hsn_code', 'size','color','weight','net_qty','packaging_type','is_active'
        ]

class ProductCollectionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Collection
        fields = ['id','name']

class ProductLinkVariantViewSerializer(serializers.ModelSerializer):
    linked_variant = ProductVariantViewSerializer(read_only=True)  # Serialize the related ProductVariant
    class Meta:
        model = ProductLinkVariant
        fields = ['id', 'product','linked_variant']  # Include the linked variant details, not just the ID

class ProductListSerializer(serializers.ModelSerializer):
    category     = CategorySerializer(read_only=True)
    subcategory  = SubcategorySerializer(read_only=True)
    subsubcategory = SubsubcategorySerializer(read_only=True)
    brand        = BrandSerializer(read_only=True)

    product_images  = ProductImageViewSerializer(many=True, required=False)
    variants        = ProductVariantViewSerializer(many=True, required=False)
    collections     = ProductCollectionSerializer(many=True, required=False)
    linked_variants = ProductLinkVariantViewSerializer(many=True,read_only=True)  # Serialize linked variants
    class Meta:
        model = Product
        fields = [
            'id','name','slug','sku','tags','description','category','subcategory','subsubcategory',
            'brand','is_active','is_published','product_images','variants','collections','linked_variants'
        ]

class ProductImageSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductImage
        fields = ['id','image','priority','alt_text','is_primary']

class ProductVariantSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductVariant
        fields = [
            'id','name','price','mrp','ssp','ean_number','hsn_code','size',
            'color','weight','net_qty','packaging_type','is_active'
        ]

class ProductDetailSerializer(serializers.ModelSerializer):
    category       = serializers.PrimaryKeyRelatedField(queryset=Category.objects.all())
    subcategory    = serializers.PrimaryKeyRelatedField(queryset=Subcategory.objects.all())
    subsubcategory = serializers.PrimaryKeyRelatedField(queryset=Subsubcategory.objects.all())
    brand          = serializers.PrimaryKeyRelatedField(queryset=Brand.objects.all())

    product_images  = ProductImageSerializer(many=True, required=False)
    variants        = ProductVariantSerializer(many=True, required=False)
    facility_ids    = serializers.ListField(child=serializers.IntegerField(), required=False, write_only=True)
    collections    = serializers.ListField(child=serializers.IntegerField(), required=False, write_only=True)
    linked_variants = serializers.ListField(child=serializers.IntegerField(), required=False, write_only=True)

    class Meta:
        model = Product
        fields = [
            'id','name','description','tags','category','subcategory','subsubcategory','brand','top_product',
            'additional_details','is_active','is_published','is_active','image_url','image',
            'product_images','variants','facility_ids', 'collections', 'linked_variants'
        ]


class CollectionProductSerializer(serializers.ModelSerializer):
    class Meta:
        model = Product
        fields = ['id', 'name']

class CollectionListSerializer(serializers.ModelSerializer):
    products = CollectionProductSerializer(many=True, required=False)
    class Meta:
        model = Collection
        fields = '__all__'
class CollectionSerializer(serializers.ModelSerializer):
    # products = CollectionProductSerializer(many=True, required=False)
    products = serializers.PrimaryKeyRelatedField(queryset=Product.objects.all(), many=True)
    class Meta:
        model = Collection
        fields = '__all__'


class ProductVariantListSerializer(serializers.ModelSerializer):
    product = CollectionProductSerializer(many=False, required=False)    
    
    class Meta:
        model = ProductVariant
        fields = '__all__'



class ProductStatusUpdateSerializer(serializers.Serializer):
    is_active = serializers.BooleanField()  # Only accepts True or False

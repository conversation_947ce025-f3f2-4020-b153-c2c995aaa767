from rest_framework import serializers
from cms.views import facility
from user.models import User
from cms.models.facility import Cluster, Facility, FacilityInventory
from cms.models.product import Product, ProductVariant


class ClusterFacilitySerializer(serializers.ModelSerializer):
    class Meta:
        model = Facility
        fields = ['id', 'name', 'facility_type']

class ClusterListSerializer(serializers.ModelSerializer):
    facilities = ClusterFacilitySerializer(many=True, read_only=True)

    class Meta:
        model = Cluster
        fields = '__all__'

class ClusterSerializer(serializers.ModelSerializer):
    # facilities = ClusterFacilitySerializer(many=True, read_only=True)
    # facilities = serializers.PrimaryKeyRelatedField(queryset=Facility.objects.all(), many=True)

    class Meta:
        model = Cluster
        fields = '__all__'

class FaciltyClusterSerializer(serializers.ModelSerializer):
    class Meta:
        model = Cluster
        fields = ['id', 'name', 'region','is_active']

class FacilitySerializer(serializers.ModelSerializer):
    manager_name = serializers.SerializerMethodField()
    clusters = FaciltyClusterSerializer(many=True, read_only=True)

    class Meta:
        model = Facility
        fields = [
            'id', 'name', 'facility_type', 'address', 'city', 'state', 'country', 'pincode',
            'latitude', 'longitude', 'servicable_area', 'is_active', 'manager', 'manager_name', 
            'clusters'
         ]

    def get_manager_name(self, obj):
        """Returns the username of the manager."""
        return obj.manager.username if obj.manager else None


class ProductVariantSerializer(serializers.ModelSerializer):
    product_name = serializers.CharField(source='product.name', read_only=True)  # Get product name

    class Meta:
        model = ProductVariant
        fields = ['id', 'name', 'product_name']
class FacilityInventorySerializer(serializers.ModelSerializer):
    product_variant_details = ProductVariantSerializer(source='product_variant', read_only=True)

    class Meta:
        model = FacilityInventory
        fields = [
            'id', 'facility', 'product_variant', 'stock', 'price','tax','csp','cust_discount',
            'max_purchase_limit','outofstock_threshold','status', 'is_active','product_variant_details'
        ]
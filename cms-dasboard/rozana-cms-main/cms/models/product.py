from modulefinder import packagePathMap
from django.db import models
from django.core.files.base import ContentFile
from django.db.models.signals import post_save, post_delete, m2m_changed, pre_delete, pre_save
from django.dispatch import receiver
from .models import TenantModel, BaseModel, ImageStorage
from .category import Category, Subcategory, Subsubcategory, Brand
from cms.utils.image_processing import convert_to_webp, process_webp_images, cleanup_images, cleanup_old_images
from django.utils.text import slugify

def product_image_upload_path(instance, filename):
    """Generate upload path for product thumbnail images"""
    return f"Product/Thumbnail/{instance.sku}.webp"

class Language(models.Model):
    name = models.CharField(max_length=50)  # e.g. "English", "Hindi", "Kannada", etc.
    
    def __str__(self):
        return self.name

class Product(BaseModel):
    name = models.CharField(max_length=255)
    slug = models.Char<PERSON>ield(max_length=255, blank=True, null=True)
    sku = models.CharField(max_length=255, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    tags = models.JSONField(default=list, blank=True, null=True)
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='products')
    subcategory = models.ForeignKey(Subcategory, on_delete=models.CASCADE, related_name='products')
    subsubcategory = models.ForeignKey(Subsubcategory, on_delete=models.CASCADE, related_name='products')
    brand = models.ForeignKey(Brand, on_delete=models.CASCADE, blank=True, null=True, related_name='products')
    top_product = models.BooleanField(default=False)
    additional_details = models.TextField(blank=True, null=True)
    is_published= models.BooleanField(default=True)
    is_active = models.BooleanField(default=True)
    image_url = models.CharField(max_length=512, blank=True, null=True)
    image = models.TextField(blank=True, null=True)

    class Meta:
        db_table = 'products'
        unique_together = ('sku',)

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super(Product, self).save(*args, **kwargs)

    def __str__(self):
        return self.name


class ProductDetail(models.Model):
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='product_details')
    language = models.ForeignKey(Language, on_delete=models.CASCADE)
    name = models.CharField(max_length=255, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    tags = models.JSONField(default=list, blank=True, null=True)

    class Meta:
        unique_together = ('product', 'language')  # Ensure each product has only one record per language

    def __str__(self):
        return f"{self.product.name} - {self.language.name}"


class ProductVariant(BaseModel):
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='variants')
    name = models.CharField(max_length=255)
    slug = models.CharField(max_length=255, blank=True, null=True)
    sku = models.CharField(max_length=255, blank=True, null=True)
    price = models.FloatField(blank=True, null=True)
    mrp = models.FloatField(blank=True, null=True)
    ssp = models.FloatField(blank=True, null=True)
    ean_number = models.BigIntegerField(blank=True, null=True)
    hsn_code = models.TextField(blank=True, null=True)
    size = models.CharField(max_length=255, blank=True, null=True)
    color = models.CharField(max_length=255, blank=True, null=True)
    weight = models.CharField(max_length=255, blank=True, null=True)
    net_qty = models.CharField(max_length=255, blank=True, null=True)
    packaging_type = models.CharField(max_length=255, blank=True, null=True)
    image = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = 'variants'
        unique_together = ('name', 'sku')

    def save(self, *args, **kwargs):
        # Automatically generate SKU if it's not provided
        if not self.sku:
            prefix = 'ROZ'
            count = ProductVariant.objects.count() + 1
            self.sku = f'{prefix}{count:02d}'

        # Generate slug
        if not self.slug:
            # Start with product name and variant name
            # Use getattr to safely access product name
            product_name = getattr(self.product, 'name', '') if self.product else ''
            slug_parts = [slugify(product_name)]

            # Append optional fields if they exist
            if self.size:
                slug_parts.append(slugify(self.size))
            if self.color:
                slug_parts.append(slugify(self.color))
            if self.weight:
                slug_parts.append(slugify(self.weight))

            # Combine all parts into one slug
            self.slug = '-'.join(slug_parts)

        super(ProductVariant, self).save(*args, **kwargs)
        
        self.create_product_details()

    def create_product_details(self):
        # Get all available languages
        languages = Language.objects.all()

        # Create or update product details for each language
        for language in languages:
            # Check if ProductDetail exists for this product and language; if not, create it
            product_detail = self.product.product_details.filter(language=language).first()
            
            if not product_detail:
                # Create a new product detail for the language if it doesn't exist
                name = self.product.name if language.name == 'English' else ''
                description = self.product.description if language.name == 'English' else ''
                tags = []  # Empty tags for non-provided languages

                ProductDetail.objects.create(
                    product=self.product,
                    language=language,
                    name=name,
                    description=description,
                    tags=tags
                )   


    def __str__(self):
        return self.name +' - '+ self.product.name

class ProductLinkVariant(BaseModel):
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='linked_variants')
    linked_variant = models.ForeignKey(ProductVariant, on_delete=models.CASCADE, related_name='linked_variant')
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = 'product_link_variants'
        unique_together = ('product', 'linked_variant')

class Collection(BaseModel):
    name        = models.CharField(max_length=255)
    description = models.TextField(null=True, blank=True)
    products    = models.ManyToManyField(Product, related_name='collections')
    is_active   = models.BooleanField(default=True)

    class Meta:
        db_table = 'collections'

    def __str__(self):
        return self.name
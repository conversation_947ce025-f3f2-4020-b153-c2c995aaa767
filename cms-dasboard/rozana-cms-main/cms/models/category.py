from django.db import models
from django.db.models.signals import post_save, post_delete, pre_delete, pre_save
from django.dispatch import receiver
from .models import TenantModel, BaseModel, ImageStorage
from cms.utils.image_processing import process_webp_images, cleanup_images, cleanup_old_images

class Category(BaseModel):
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    image = models.ImageField(
        upload_to="Category/",
        storage=ImageStorage(),
        null=True,
        blank=True,
        help_text="Upload image file. Original stored with filename, WebP versions generated automatically."
    )
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = 'categories'
        ordering = ['name']
        verbose_name_plural = 'Categories'
    
    def __str__(self):
        return self.name


class Subcategory(BaseModel):
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='subcategories')
    image = models.ImageField(
        upload_to='Subcategory/',
        storage=ImageStorage(),
        null=True,
        blank=True,
        help_text="Upload image file. Original stored with filename, WebP versions generated automatically."
    )
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = 'subcategories'
        ordering = ['category__name', 'name']
        verbose_name_plural = 'Subcategories'
    
    def __str__(self):
        return f"{self.name} - {self.category.name}"


class Subsubcategory(BaseModel):
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='subsubcategories')
    subcategory = models.ForeignKey(Subcategory, on_delete=models.CASCADE, related_name='subsubcategories')
    image = models.ImageField(
        upload_to='Childsubcategory/',
        storage=ImageStorage(),
        null=True,
        blank=True,
        help_text="Upload image file. Original stored with filename, WebP versions generated automatically."
    )
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = 'subsubcategories'
        ordering = ['name']
        verbose_name_plural = 'Subsubcategories'
    
    def __str__(self):
        return f"{self.name} - {self.subcategory.name} - {self.category.name}"


class Brand(BaseModel):
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    image = models.ImageField(
        upload_to='Brand/',
        storage=ImageStorage(),
        null=True,
        blank=True,
        help_text="Upload image file. Original stored with filename, WebP versions generated automatically."
    )
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = 'brands'
        ordering = ['name']
        verbose_name_plural = 'Brands'
    
    def __str__(self):
        return self.name
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views.facility import FacilityViewSet, ClusterViewSet, FacilityInventoryViewSet
from .views.category import CategoryViewSet, SubcategoryViewSet, SubsubcategoryViewSet, BrandViewSet
from .views.product import ProductViewSet, CollectionViewSet, ProductVariantViewSet, ProductStatusUpdateView, ProductListViewSet
from .views.common import MediaFileUploadView

router = DefaultRouter()
router.register(r'clusters', ClusterViewSet)
router.register(r'facilities', FacilityViewSet)
router.register(r'facilityinventory', FacilityInventoryViewSet)
router.register(r'categories', CategoryViewSet)
router.register(r'subcategories', SubcategoryViewSet)
router.register(r'subsubcategories', SubsubcategoryViewSet)
router.register(r'brands', BrandViewSet)
router.register(r'products', ProductViewSet, basename='products')
router.register(r'collections', CollectionViewSet, basename='collections')
router.register(r'variants', ProductVariantViewSet, basename='variants')

urlpatterns = [
    path('', include(router.urls)),
    path('products/<int:product_id>/status/', ProductStatusUpdateView.as_view(), name='product-status-update'),
    path('product/list/', ProductListViewSet.as_view({'get': 'list'}), name='product-list'),
    path('upload/', MediaFileUploadView.as_view(), name='upload-files'),
]

from django.contrib import admin
from .models.category import Category, Subcategory, Subsubcategory, Brand
from .models.facility import Facility, Cluster, FacilityInventory
from .models.product import Language, Product, ProductDetail, ProductVariant, Collection
from .models.product_image import ProductImage
from .models.master import Tax
from django.utils.html import format_html


@admin.register(Tax)
class TaxAdmin(admin.ModelAdmin):
    list_display = ('name', 'percentage', 'cgst_percentage', 'sgst_percentage', 'igst_percentage', 'is_active')
    search_fields = ('name', 'percentage', 'is_active')

    fieldsets = (
        (None, {
            'fields': ('name', 'description', 'percentage', 'cgst_percentage', 'sgst_percentage', 'igst_percentage', 'cess_percentage', 'is_active')
        }),
    )

@admin.register(Cluster)
class ClusterAdmin(admin.ModelAdmin):
    list_display = ('name', 'region', 'latitude', 'longitude', 'is_active')
    search_fields = ('name', 'region')
    list_filter = ('region', 'is_active')
    filter_horizontal = ('facilities',) 

@admin.register(Facility)
class FacilityAdmin(admin.ModelAdmin):
    list_display = ('name', 'facility_type', 'address', 'city', 'state', 'get_assigned_clusters', 'is_active')
    search_fields = ('name', 'address', 'city', 'state')
    list_filter = ('facility_type', 'is_active', 'city')

    def get_assigned_clusters(self, obj):
        # This will return a comma-separated string of cluster names assigned to the facility
        return ", ".join([cluster.name for cluster in obj.clusters.all()])
    get_assigned_clusters.short_description = "Clusters"
 

@admin.register(FacilityInventory)
class FacilityInventoryAdmin(admin.ModelAdmin):
    list_display = ('facility', 'product_variant', 'stock', 'price', 'is_active')
    search_fields = ('facility__name', 'product_variant__name')  # You can search by the manager's username

    # Include manager field in the admin form
    fieldsets = (
        (None, {
            'fields': ('facility', 'product_variant', 'stock', 'price', 'tax','csp','cust_discount','max_purchase_limit','outofstock_threshold','status','is_active')
        }),
    )


@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'is_active', 'has_image')
    list_filter = ('is_active',)
    search_fields = ('name', 'description')
    readonly_fields = ('creation_date', 'updation_date')

    fieldsets = (
        (None, {
            'fields': ('name', 'description')
        }),
        ('Images', {
            'fields': ('image',),
            'description': 'Upload an image file (stored with original filename, WebP versions generated automatically) OR provide a direct URL.'
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('Timestamps', {
            'fields': ('creation_date', 'updation_date'),
            'classes': ('collapse',)
        }),
    )

    def has_image(self, obj):
        return bool(obj.image)
    has_image.boolean = True
    has_image.short_description = 'Has Image'


@admin.register(Subcategory)
class SubcategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'category', 'is_active', 'has_image')
    list_filter = ('category', 'is_active')
    search_fields = ('name', 'description')
    readonly_fields = ('creation_date', 'updation_date')

    fieldsets = (
        (None, {
            'fields': ('name', 'description', 'category')
        }),
        ('Images', {
            'fields': ('image',),
            'description': 'Upload an image file (stored with original filename, WebP versions generated automatically) OR provide a direct URL.'
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('Timestamps', {
            'fields': ('creation_date', 'updation_date'),
            'classes': ('collapse',)
        }),
    )

    def has_image(self, obj):
        return bool(obj.image)
    has_image.boolean = True
    has_image.short_description = 'Has Image'

@admin.register(Subsubcategory)
class SubsubcategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'category', 'subcategory', 'is_active', 'has_image')
    list_filter = ('category', 'is_active')
    search_fields = ('name', 'description')
    readonly_fields = ('creation_date', 'updation_date')

    fieldsets = (
        (None, {
            'fields': ('name', 'description', 'category', 'subcategory')
        }),
        ('Images', {
            'fields': ('image',),
            'description': 'Upload an image file (stored with original filename, WebP versions generated automatically) OR provide a direct URL.'
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('Timestamps', {
            'fields': ('creation_date', 'updation_date'),
            'classes': ('collapse',)
        }),
    )

    def has_image(self, obj):
        return bool(obj.image)
    has_image.boolean = True
    has_image.short_description = 'Has Image'


@admin.register(Brand)
class BrandAdmin(admin.ModelAdmin):
    list_display = ('name', 'is_active', 'has_image')
    list_filter = ('is_active',)
    search_fields = ('name', 'description')
    readonly_fields = ('creation_date', 'updation_date')

    fieldsets = (
        (None, {
            'fields': ('name', 'description')
        }),
        ('Images', {
            'fields': ('image',),
            'description': 'Upload an image file (stored with original filename, WebP versions generated automatically) OR provide a direct URL.'
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('Timestamps', {
            'fields': ('creation_date', 'updation_date'),
            'classes': ('collapse',)
        }),
    )

    def has_image(self, obj):
        return bool(obj.image)
    has_image.boolean = True
    has_image.short_description = 'Has Image'


# class ProductImageInline(admin.TabularInline):
#     """
#     Inline admin for ProductImage model to allow multiple image uploads per product
#     """
#     model = ProductImage
#     extra = 1
#     max_num = 10
#     fields = ('image', 'priority', 'alt_text', 'is_primary')


# @admin.register(ProductImage)
# class ProductImageAdmin(admin.ModelAdmin):
#     """
#     Standalone admin for ProductImage model (optional, for advanced management)
#     """
#     list_display = ('product', 'priority', 'is_primary', 'alt_text')
#     list_filter = ('is_primary', 'product__brand')
#     search_fields = ('product__name', 'product__sku', 'alt_text')


# @admin.register(Product)
# class ProductAdmin(admin.ModelAdmin):
#     list_display = ('name', 'sku', 'price', 'brand', 'facility', 'is_active', 'has_thumbnail_image', 'image_count')
#     list_filter = ('is_active', 'facility', 'brand', 'subcategories')
#     search_fields = ('name', 'sku', 'description')
#     readonly_fields = ('creation_date', 'updation_date')
#     filter_horizontal = ('subcategories',)
#     inlines = [ProductImageInline]

#     fieldsets = (
#         (None, {
#             'fields': ('name', 'description', 'sku', 'price')
#         }),
#         ('Categorization', {
#             'fields': ('subcategories', 'brand', 'colour')
#         }),
#         ('Facility', {
#             'fields': ('facility',)
#         }),
#         ('Product Details', {
#             'fields': ('weight', 'weight_unit', 'display_alias', 'tags', 'attributes')
#         }),
#         ('Images', {
#             'fields': ('thumbnail_image', 'thumbnail_url', 'image_url'),
#             'description': 'Upload a thumbnail image (converted to WebP) OR provide direct URLs. All fields are independent.',
#         }),
#         ('Status', {
#             'fields': ('is_active',)
#         }),
#         ('Timestamps', {
#             'fields': ('creation_date', 'updation_date'),
#             'classes': ('collapse',)
#         }),
#     )

#     def has_thumbnail_image(self, obj):
#         return bool(obj.thumbnail_image)
#     has_thumbnail_image.boolean = True
#     has_thumbnail_image.short_description = 'Has Thumbnail'

#     def image_count(self, obj):
#         return obj.product_images.count()
#     image_count.short_description = "Images"

class LanguageAdmin(admin.ModelAdmin):
    list_display = ('name',)

admin.site.register(Language, LanguageAdmin)

class ProductImageInline(admin.TabularInline):
    model = ProductImage
    extra = 1  # How many empty image fields to show by default in the admin


class ProductVariantInline(admin.TabularInline):
    model = ProductVariant
    extra = 1  # How many empty variant fields to show by default in the admin

class ProductDetailInline(admin.TabularInline):
    model = ProductDetail
    extra = 0  # No extra blank rows will appear
    fields = ['language', 'name', 'description', 'tags']
    show_change_link = True  # Allow links to change the related product detail in admin

    def get_formset(self, request, obj=None, **kwargs):
        formset = super().get_formset(request, obj, **kwargs)
        # Limit product detail formset to only certain languages (e.g., English, Hindi, etc.)
        formset.form.base_fields['language'].queryset = Language.objects.all()
        return formset


class ProductAdmin(admin.ModelAdmin):
    list_display = ('name', 'slug', 'sku', 'category', 'subcategory', 'brand', 'is_active', 'is_published', 'image_url', 'facility_ids_display')
    search_fields = ('name', 'slug', 'category__name', 'subcategory__name', 'brand__name')
    list_filter = ('is_active', 'is_published', 'category', 'subcategory', 'brand')
    prepopulated_fields = {'slug': ('name',)}  # Automatically create a slug from the name
    inlines = [ProductVariantInline, ProductImageInline, ProductDetailInline]  # Add variants and images inline

    def image_preview(self, obj):
        """To show the product image preview in the admin list."""
        if obj.image:
            return format_html('<img src="{}" style="height: 50px;" />', obj.image.url)
        return "No Image"
    image_preview.short_description = 'Image Preview'

    def facility_ids_display(self, obj):
        """Displays a list of facility names associated with the product."""
        facilities = Facility.objects.filter(facility_inventories__product_variant__product=obj)
        return ", ".join([facility.name for facility in facilities])
    facility_ids_display.short_description = 'Associated Facilities'

    def save_model(self, request, obj, form, change):
        # Save the Product instance first
        super().save_model(request, obj, form, change)

        # Create ProductDetail records for all languages after the product is saved
        self.create_product_details(obj)

    def create_product_details(self, obj):
        """Create ProductDetail records for all available languages."""
        languages = Language.objects.all()  # Get all languages available

        # Create ProductDetail records for each language
        for language in languages:
            # Check if ProductDetail exists for this product and language; if not, create it
            product_detail = obj.product_details.filter(language=language).first()

            if not product_detail:
                # If no ProductDetail exists, create it with empty fields for non-provided languages
                name = obj.name if language.name == 'English' else ''  # Default to English name
                description = obj.description if language.name == 'English' else ''
                tags = []  # Empty tags for non-provided languages

                # Create ProductDetail record for the language
                ProductDetail.objects.create(
                    product=obj,
                    language=language,
                    name=name,
                    description=description,
                    tags=tags
                )

class ProductDetailAdmin(admin.ModelAdmin):
    list_display = ('product', 'language','name','description','tags')

admin.site.register(ProductDetail, ProductDetailAdmin)

class ProductVariantAdmin(admin.ModelAdmin):
    list_display = ('name', 'sku', 'slug', 'price', 'mrp', 'is_active', 'product', 'facility_ids_display')
    search_fields = ('name', 'sku', 'product__name')
    list_filter = ('is_active', 'product', 'size', 'color')

    def product(self, obj):
        return obj.product.name
    product.short_description = 'Product Name'

    def facility_ids_display(self, obj):
        """Displays a list of facilities associated with the variant."""
        facilities = Facility.objects.filter(facility_inventories__product_variant=obj)
        return ", ".join([facility.name for facility in facilities])
    facility_ids_display.short_description = 'Associated Facilities'


class ProductImageAdmin(admin.ModelAdmin):
    list_display = ('product', 'priority', 'is_primary', 'image', 'alt_text')
    search_fields = ('product__name', 'alt_text')
    list_filter = ('product', 'is_primary', 'priority')


# Register models in the admin interface
admin.site.register(Product, ProductAdmin)
admin.site.register(ProductVariant, ProductVariantAdmin)
admin.site.register(ProductImage, ProductImageAdmin)

class CollectionAdmin(admin.ModelAdmin):
    # Display these fields in the list view
    list_display = ('name', 'description',)
    
    # Make the description field editable in the list view (optional)
    # list_editable = ('description',)

    # Search functionality to easily find collections by name or description
    search_fields = ('name', 'description')

    # Filters for narrowing down results in the admin
    # list_filter = ('created_at', 'updated_at')

    # For the Many-to-Many relationship field to add/remove products
    filter_horizontal = ('products',)  # Use this to add a more user-friendly interface for selecting products

    # The form layout can be customized here if desired
    fieldsets = (
        (None, {
            'fields': ('name', 'description', 'products'),
        }),
    )

# Register the model with the custom admin class
admin.site.register(Collection, CollectionAdmin)

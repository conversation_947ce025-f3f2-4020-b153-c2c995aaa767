from rest_framework import viewsets
from cms.models.category import Category, Subcategory, Subsubcategory, Brand
from cms.serializers.category import CategorySerializer, CategoryListSerializer, SubcategorySerializer, SubsubcategorySerializer, BrandSerializer
from rest_framework.permissions import IsAuthenticated
from rest_framework.filters import SearchFilter
from cms.utils.pagination import CustomPageNumberPagination


class CategoryViewSet(viewsets.ModelViewSet):
    """
    API endpoint that allows categories to be viewed, created, updated, or deleted.
    """
    queryset = Category.objects.all()
    permission_classes = [IsAuthenticated]
    pagination_class = CustomPageNumberPagination
    filter_backends = (SearchFilter,)
    search_fields = ['name']

    def get_serializer_class(self):
        if self.action == 'list' or self.action == 'retrieve':
            return CategoryListSerializer
        else:
            return CategorySerializer

class SubcategoryViewSet(viewsets.ModelViewSet):
    """
    API endpoint that allows subcategories to be viewed, created, updated, or deleted.
    """
    queryset = Subcategory.objects.all()
    serializer_class = SubcategorySerializer
    permission_classes = [IsAuthenticated]
    pagination_class = CustomPageNumberPagination  # Add pagination class here
    filter_backends = (SearchFilter,)  # Add SearchFilter
    search_fields = ['name']

class SubsubcategoryViewSet(viewsets.ModelViewSet):
    """
    API endpoint that allows subsubcategories to be viewed, created, updated, or deleted.
    """
    queryset = Subsubcategory.objects.all()
    serializer_class = SubsubcategorySerializer
    permission_classes = [IsAuthenticated]
    pagination_class = CustomPageNumberPagination  # Add pagination class here
    filter_backends = (SearchFilter,)  # Add SearchFilter
    search_fields = ['name']

class BrandViewSet(viewsets.ModelViewSet):
    """
    API endpoint that allows brands to be viewed, created, updated, or deleted.
    """
    queryset = Brand.objects.all()
    serializer_class = BrandSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = CustomPageNumberPagination  # Add pagination class here
    filter_backends = (SearchFilter,)  # Add SearchFilter
    search_fields = ['name']

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser
from django.core.files.storage import default_storage, FileSystemStorage  
from rest_framework import status
from django.conf import settings
import time

class MediaFileUploadView(APIView):
    parser_classes = [MultiPartParser, FormParser]

    def post(self, request, *args, **kwargs):
        uploaded_files_data = []

        try:
            uploaded_files = request.FILES.getlist('files')

            for uploaded_file in uploaded_files:
                timestamp = str(int(time.time()))
                filename = f"{timestamp}_{uploaded_file.name}"

                if settings.USE_S3:
                    file_path = default_storage.save(f"image/{filename}", uploaded_file)
                    file_url = default_storage.url(file_path)
                else:
                    fs = FileSystemStorage()
                    filename = fs.save(filename, uploaded_file)
                    file_url = fs.url(filename)

                uploaded_files_data.append({
                    'file_path': file_url
                })

            return Response(uploaded_files_data, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response({"error": "File upload failed"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
       

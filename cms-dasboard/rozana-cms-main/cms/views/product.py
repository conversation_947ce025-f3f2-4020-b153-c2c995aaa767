from rest_framework import viewsets, status
from rest_framework.permissions import IsAuthenticated
from cms.models.product import Product, ProductVariant, Collection, ProductLinkVariant
from cms.models.product_image import ProductImage
from cms.models.facility import Facility, FacilityInventory
from cms.serializers.product import ProductListSerializer, ProductDetailSerializer, CollectionSerializer, CollectionListSerializer, ProductVariantListSerializer, ProductStatusUpdateSerializer
from rest_framework.filters import SearchFilter
from cms.utils.pagination import CustomPageNumberPagination
from rest_framework.response import Response
from rest_framework.views import APIView


class CollectionViewSet(viewsets.ModelViewSet):
    queryset = Collection.objects.all()
    # serializer_class = CollectionSerializer
    permission_classes  = [IsAuthenticated]
    pagination_class    = CustomPageNumberPagination
    filter_backends     = (SearchFilter,)
    search_fields       = ['name']

    def get_serializer_class(self):
        if self.action == 'list' or self.action == 'retrieve':
            return CollectionListSerializer
        else:
            return CollectionSerializer

class ProductViewSet(viewsets.ModelViewSet):
    queryset = Product.objects.all()
    permission_classes = [IsAuthenticated]
    pagination_class = CustomPageNumberPagination
    filter_backends = (SearchFilter,)
    search_fields = ['name', 'description', 'slug', 'tags']

    def get_queryset(self):
        current_user = self.request.user  # More descriptive name for the user
        if current_user.role == 'manager':
            managed_facilities = Facility.objects.filter(manager=current_user)  # Managed facilities
            product_variant_ids = FacilityInventory.objects.filter(
                facility__in=managed_facilities
            ).values_list('product_variant', flat=True)  # Product variant IDs associated with managed facilities
            return Product.objects.filter(variants__in=product_variant_ids).distinct()  # Filtering products based on variants
        return Product.objects.all()


    def get_serializer_class(self):
        if self.action == 'list' or self.action == 'retrieve':
            return ProductListSerializer  # Serializer for list and retrieve actions
        return ProductDetailSerializer  # Default serializer for other actions like create and update

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data

        product_images = validated_data.pop('product_images', [])  # Handling product images
        product_variants = validated_data.pop('variants', [])  # Handling product variants
        associated_facility_ids = validated_data.pop('facility_ids', [])  # Handling facility associations
        collection_ids = validated_data.pop('collections', [])  # Handling collections
        linked_variant_ids = validated_data.pop('linked_variants', [])  # Handling linked products

        # Creating the product object
        new_product = Product.objects.create(**validated_data)

        # Creating the product images
        for image_data in product_images:
            ProductImage.objects.create(product=new_product, **image_data)

        # Creating the product variants and associating them with the new product
        created_variants = []
        for variant_data in product_variants:
            product_variant = ProductVariant.objects.create(product=new_product, **variant_data)
            created_variants.append(product_variant)
        new_product.variants.set(created_variants)

        # Handling facility inventory creation for associated facilities
        for facility_id in associated_facility_ids:
            try:
                facility = Facility.objects.get(pk=facility_id)
            except Facility.DoesNotExist:
                # Optionally, log or handle the missing facility as needed
                continue
            missing_inventory = [
                FacilityInventory(facility=facility, product_variant=product_variant, stock=0, price=0.0)
                for product_variant in created_variants
                if not FacilityInventory.objects.filter(facility=facility, product_variant=product_variant).exists()
            ]
            FacilityInventory.objects.bulk_create(missing_inventory)

        # Managing collections associated with the new product
        for collection_id in collection_ids:
            try:
                collection = Collection.objects.get(pk=collection_id)
                new_product.collections.add(collection)
            except Collection.DoesNotExist:
                # Optionally, log or handle the missing collection as needed
                continue

        # Linking products
        for linked_variant_id in linked_variant_ids:
            try:
                linked_variant = ProductVariant.objects.get(pk=linked_variant_id)
                # Create ProductLinkVariant instance
                ProductLinkVariant.objects.create(product=new_product, linked_variant=linked_variant)
            except ProductVariant.DoesNotExist:
                # Optionally, log or handle the missing variant as needed
                continue

        # Returning the created product details
        product_details = ProductDetailSerializer(new_product, context={'request': request})
        return Response(product_details.data, status=status.HTTP_201_CREATED)

    def update(self, request, *args, **kwargs):
        product = self.get_object()
        serializer = self.get_serializer(product, data=request.data, partial=kwargs.pop('partial', False))
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data

        product_images = validated_data.pop('product_images', [])  # Handling product images
        product_variants = validated_data.pop('variants', [])  # Handling product variants
        associated_facility_ids = validated_data.pop('facility_ids', [])  # Handling facility associations
        collection_ids = validated_data.pop('collections', [])  # Handling collections
        linked_variant_ids = validated_data.pop('linked_variants', [])  # Handling linked products

        # Updating the product fields
        for field, value in validated_data.items():
            setattr(product, field, value)
        product.save()

        new_image_ids = []
        existing_image_ids = [img.id for img in product.product_images.all()]
        existing_image_ids_kept = []
        for image_data in product_images:
            image_id = image_data.get('id', None)
            if image_id and image_id in existing_image_ids:
                existing_image = ProductImage.objects.get(id=image_id)
                for field, value in image_data.items():
                    setattr(existing_image, field, value)
                existing_image.save()
                existing_image_ids_kept.append(existing_image.id)
            else:
                new_image = ProductImage.objects.create(product=product, **image_data)
                new_image_ids.append(new_image.id)
        image_ids_to_keep = existing_image_ids_kept + new_image_ids
        product.product_images.exclude(id__in=image_ids_to_keep).delete()

        # Handling product variants (updating or creating new variants)
        existing_variant_ids = {variant.id: variant for variant in product.variants.all()}
        new_variant_ids = []

        for variant_data in product_variants:
            variant_id = variant_data.get('id', None)
            if variant_id and variant_id in existing_variant_ids:
                existing_variant = existing_variant_ids[variant_id]
                for field, value in variant_data.items():
                    setattr(existing_variant, field, value)
                existing_variant.save()
            else:
                new_variant = ProductVariant.objects.create(product=product, **variant_data)
                new_variant_ids.append(new_variant.id)

        # Correctly using .set() for assigning variants
        variant_ids_to_keep = new_variant_ids + [variant_data.get('id', None) for variant_data in product_variants if variant_data.get('id', None)]
        product.variants.set(ProductVariant.objects.filter(id__in=variant_ids_to_keep))


        # Handling linked products
        existing_linked_variants = {link.linked_variant.id: link for link in product.linked_variants.all()}
        for linked_variant_id in linked_variant_ids:
            try:
                linked_variant = ProductVariant.objects.get(pk=linked_variant_id)
                if linked_variant_id in existing_linked_variants:
                    # Update existing link if needed
                    existing_linked_variants[linked_variant_id].save()
                else:
                    # Create new link
                    ProductLinkVariant.objects.create(product=product, linked_variant=linked_variant)
            except ProductVariant.DoesNotExist:
                continue  # Optionally, log or handle the missing variant as needed

        # Handling facility inventory updates
        for facility_id in associated_facility_ids:
            try:
                facility = Facility.objects.get(pk=facility_id)
            except Facility.DoesNotExist:
                continue  # Optionally, log or handle the missing facility as needed
            missing_inventory = [
                FacilityInventory(facility=facility, product_variant=product_variant, stock=0, price=0.0)
                for product_variant in product.variants.all()
                if not FacilityInventory.objects.filter(facility=facility, product_variant=product_variant).exists()
            ]
            FacilityInventory.objects.bulk_create(missing_inventory)

        # Managing product collections
        product.collections.clear()
        for collection_id in collection_ids:
            try:
                collection = Collection.objects.get(pk=collection_id)
                product.collections.add(collection)
            except Collection.DoesNotExist:
                continue  # Optionally, log or handle the missing collection as needed

        # Returning the updated product details
        product_details = ProductDetailSerializer(product, context={'request': request})
        return Response(product_details.data)

class ProductStatusUpdateView(APIView):
    """
    A view to update the status (boolean field) of a product.
    """

    def patch(self, request, *args, **kwargs):
        # Get the product by ID
        product_id = kwargs.get('product_id')
        try:
            product = Product.objects.get(id=product_id)
        except Product.DoesNotExist:
            return Response({"detail": "Product not found."}, status=status.HTTP_404_NOT_FOUND)

        # Deserialize the request data to get the status
        serializer = ProductStatusUpdateSerializer(data=request.data)
        if serializer.is_valid():
            # Update the product's status
            product.is_active = serializer.validated_data['is_active']
            product.save()

            return Response({"status": "Product status updated successfully."}, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ProductVariantViewSet(viewsets.ModelViewSet):
    queryset = ProductVariant.objects.all()
    permission_classes  = [IsAuthenticated]
    pagination_class    = CustomPageNumberPagination
    filter_backends     = (SearchFilter,)
    search_fields       = ['name']

    def get_serializer_class(self):
        # if self.action == 'list' or self.action == 'retrieve':
        #     return ProductVariantSerializer
        # else:
        #     return ProductVariantSerializer
        return ProductVariantListSerializer

class ProductListViewSet(viewsets.ModelViewSet):
    queryset = Product.objects.all()

    def get_serializer_class(self):
        return ProductListSerializer
# Python bytecode files
*.pyc
*.pyo
*.pyd
__pycache__/
*.so

# Django
*.log
*.pot
*.pyc
__pycache__

# VS Code settings
.vscode/

# PyCharm settings
.idea/

# Virtual environment
venv/
env/
ENV/
.venv/
.conda/

# Jupyter Notebooks
.ipynb_checkpoints/

# Local Django development environment
local_settings.py
settings_local.py

# Coverage reports
coverage/
*.cover
*.hypothesis/

# Translations
*.mo
*.po

# Python distribution packages
*.egg
*.egg-info
dist/
build/

# Test output
nosetests.xml
coverage.xml
*.cover
*.hypothesis/
